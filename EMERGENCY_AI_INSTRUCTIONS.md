# 🚨 EMERGENCY AI INSTRUCTIONS - KATE MUST WORK TODAY

## CRITICAL MISSION
Fix Kate's non-functional UI and make it fully operational. User gets fired if this doesn't work today.

## CURRENT STATUS
- <PERSON> starts successfully (logs show "<PERSON><PERSON> should be visible")
- UI displays but is completely non-responsive to user input
- All backend services are working (Ollama, database, etc.)
- Problem is 100% in the UI interaction layer

## PHASE 1: EMERGENCY UI FIX (MUST COMPLETE FIRST)

### STEP 1: Run Emergency Diagnostics
```bash
# Run this IMMEDIATELY to understand the problem
python emergency_ui_test.py
```

### STEP 2: Fix UI Event System
**Most likely causes (fix in this order):**
1. **QApplication event loop not running properly**
2. **Widget focus/interaction policies broken**
3. **Signal/slot connections not established**
4. **Main thread blocked by synchronous operations**

### STEP 3: Test Basic Chat Flow
**Success criteria:**
- User can type in message input field
- Send button responds to clicks
- Messages appear in chat area
- <PERSON><PERSON><PERSON> responds with messages

## EMERGENCY RECOVERY COMMANDS

### If you break something:
```bash
git checkout HEAD~1  # Go back one commit
./start_kate.sh      # Test if it still starts
```

### If Kate won't start:
```bash
git checkout main    # Go back to known working state
./start_kate.sh      # Should at least start with non-functional UI
```

## SUCCESS CRITERIA FOR TODAY
- [ ] User can type messages and get AI responses
- [ ] Basic conversation works end-to-end
- [ ] No crashes or errors
- [ ] UI is responsive to clicks and keyboard input

## RULES FOR AI AGENT
1. **ONLY fix the UI interaction problem - don't add features**
2. **Test every single change immediately**
3. **Commit working changes before trying next fix**
4. **If stuck for more than 30 minutes, try different approach**
5. **Focus on making basic chat work - ignore advanced features**

## EMERGENCY CONTACTS
If AI gets completely stuck, the problem is likely:
- Qt event loop configuration in app/main.py
- Widget parenting issues in app/ui/main_window.py
- Signal connections in app/ui/components/chat_area.py

## TIME LIMIT: 8 HOURS MAX
This MUST be working by end of day or user gets fired.
