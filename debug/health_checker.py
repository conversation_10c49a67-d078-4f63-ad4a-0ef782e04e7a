"""
Application Health Checker for Kate LLM Client.

Comprehensive health monitoring system that validates all components
and provides detailed health reports for AI debugging and monitoring.
"""

import asyncio
import json
import subprocess
import sys
import time
from dataclasses import asdict, dataclass
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

from loguru import logger


class HealthStatus(Enum):
    """Health status levels"""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"


@dataclass
class HealthCheck:
    """Individual health check result"""
    check_name: str
    status: HealthStatus
    message: str
    details: Dict[str, Any]
    timestamp: str
    duration_ms: float
    recommendations: List[str]


@dataclass
class ComponentHealth:
    """Health status of a system component"""
    component_name: str
    overall_status: HealthStatus
    checks: List[HealthCheck]
    dependencies: List[str]
    critical_issues: List[str]
    warnings: List[str]


@dataclass
class SystemHealthReport:
    """Comprehensive system health report"""
    timestamp: str
    overall_status: HealthStatus
    components: List[ComponentHealth]
    summary: Dict[str, Any]
    recommendations: List[str]
    next_check_time: Optional[str] = None


class ApplicationHealthChecker:
    """
    Comprehensive health checker for Kate application.
    
    Monitors all system components and provides detailed health reports
    for AI debugging and system monitoring.
    """
    
    def __init__(self):
        self.logger = logger.bind(component="ApplicationHealthChecker")
        self.last_health_report: Optional[SystemHealthReport] = None
        self.health_history: List[SystemHealthReport] = []
    
    async def run_full_health_check(self) -> SystemHealthReport:
        """Run comprehensive health check of all system components"""
        
        self.logger.info("Starting comprehensive health check...")
        start_time = time.time()
        
        # Check all components
        components = []
        
        # Core application health
        app_health = await self._check_application_health()
        components.append(app_health)
        
        # Database health
        db_health = await self._check_database_health()
        components.append(db_health)
        
        # UI health
        ui_health = await self._check_ui_health()
        components.append(ui_health)
        
        # Service health
        service_health = await self._check_services_health()
        components.append(service_health)
        
        # Dependency health
        dependency_health = await self._check_dependencies_health()
        components.append(dependency_health)
        
        # File system health
        filesystem_health = await self._check_filesystem_health()
        components.append(filesystem_health)
        
        # Determine overall status
        overall_status = self._determine_overall_status(components)
        
        # Generate summary
        summary = self._generate_health_summary(components)
        
        # Generate recommendations
        recommendations = self._generate_health_recommendations(components)
        
        # Create health report
        health_report = SystemHealthReport(
            timestamp=datetime.now().isoformat(),
            overall_status=overall_status,
            components=components,
            summary=summary,
            recommendations=recommendations,
            next_check_time=(datetime.now().timestamp() + 3600)  # Next check in 1 hour
        )
        
        # Store report
        self.last_health_report = health_report
        self.health_history.append(health_report)
        
        # Keep only last 24 reports
        if len(self.health_history) > 24:
            self.health_history = self.health_history[-24:]
        
        duration = (time.time() - start_time) * 1000
        self.logger.info(f"Health check completed in {duration:.1f}ms - Status: {overall_status.value}")
        
        return health_report
    
    async def _check_application_health(self) -> ComponentHealth:
        """Check core application health"""
        checks = []
        
        # Check if Kate can be imported
        import_check = await self._check_kate_imports()
        checks.append(import_check)
        
        # Check configuration
        config_check = await self._check_configuration()
        checks.append(config_check)
        
        # Check logging system
        logging_check = await self._check_logging_system()
        checks.append(logging_check)
        
        # Check event loop
        event_loop_check = await self._check_event_loop()
        checks.append(event_loop_check)
        
        return self._create_component_health("Application Core", checks, ["Python", "asyncio", "loguru"])
    
    async def _check_database_health(self) -> ComponentHealth:
        """Check database health"""
        checks = []
        
        # Check database file exists
        db_file_check = await self._check_database_file()
        checks.append(db_file_check)
        
        # Check database connection
        db_connection_check = await self._check_database_connection()
        checks.append(db_connection_check)
        
        # Check database schema
        db_schema_check = await self._check_database_schema()
        checks.append(db_schema_check)
        
        return self._create_component_health("Database", checks, ["SQLite", "SQLAlchemy"])
    
    async def _check_ui_health(self) -> ComponentHealth:
        """Check UI system health"""
        checks = []
        
        # Check Qt availability
        qt_check = await self._check_qt_availability()
        checks.append(qt_check)
        
        # Check display environment
        display_check = await self._check_display_environment()
        checks.append(display_check)
        
        # Check UI components
        ui_components_check = await self._check_ui_components()
        checks.append(ui_components_check)
        
        return self._create_component_health("User Interface", checks, ["PySide6", "Qt", "Display"])
    
    async def _check_services_health(self) -> ComponentHealth:
        """Check background services health"""
        checks = []
        
        # Check Ollama connection
        ollama_check = await self._check_ollama_service()
        checks.append(ollama_check)
        
        # Check embedding service
        embedding_check = await self._check_embedding_service()
        checks.append(embedding_check)
        
        # Check vector store
        vector_store_check = await self._check_vector_store()
        checks.append(vector_store_check)
        
        return self._create_component_health("Services", checks, ["Ollama", "Embeddings", "Vector Store"])
    
    async def _check_dependencies_health(self) -> ComponentHealth:
        """Check system dependencies health"""
        checks = []
        
        # Check Python version
        python_check = await self._check_python_version()
        checks.append(python_check)
        
        # Check required packages
        packages_check = await self._check_required_packages()
        checks.append(packages_check)
        
        # Check virtual environment
        venv_check = await self._check_virtual_environment()
        checks.append(venv_check)
        
        return self._create_component_health("Dependencies", checks, ["Python", "pip", "Virtual Environment"])
    
    async def _check_filesystem_health(self) -> ComponentHealth:
        """Check file system health"""
        checks = []
        
        # Check disk space
        disk_space_check = await self._check_disk_space()
        checks.append(disk_space_check)
        
        # Check file permissions
        permissions_check = await self._check_file_permissions()
        checks.append(permissions_check)
        
        # Check required directories
        directories_check = await self._check_required_directories()
        checks.append(directories_check)
        
        return self._create_component_health("File System", checks, ["Disk Space", "Permissions"])
    
    async def _check_kate_imports(self) -> HealthCheck:
        """Check if Kate modules can be imported"""
        start_time = time.time()
        
        try:
            # Try importing core Kate modules
            sys.path.insert(0, str(Path.cwd() / "app"))
            
            from app.core.application import KateApplication
            from app.database.manager import DatabaseManager
            from app.ui.main_window import MainWindow
            
            return HealthCheck(
                check_name="Kate Module Imports",
                status=HealthStatus.HEALTHY,
                message="All core Kate modules can be imported successfully",
                details={"modules_checked": ["KateApplication", "MainWindow", "DatabaseManager"]},
                timestamp=datetime.now().isoformat(),
                duration_ms=(time.time() - start_time) * 1000,
                recommendations=[]
            )
            
        except ImportError as e:
            return HealthCheck(
                check_name="Kate Module Imports",
                status=HealthStatus.CRITICAL,
                message=f"Failed to import Kate modules: {str(e)}",
                details={"error": str(e), "import_path": str(Path.cwd() / "app")},
                timestamp=datetime.now().isoformat(),
                duration_ms=(time.time() - start_time) * 1000,
                recommendations=[
                    "Check if all required dependencies are installed",
                    "Verify PYTHONPATH includes the app directory",
                    "Check for syntax errors in Kate modules"
                ]
            )
    
    async def _check_configuration(self) -> HealthCheck:
        """Check application configuration"""
        start_time = time.time()
        
        try:
            config_files = [
                "app/core/config.py",
                "assistants.json",
                "themes/kate_dark.json"
            ]
            
            missing_files = []
            for config_file in config_files:
                if not Path(config_file).exists():
                    missing_files.append(config_file)
            
            if missing_files:
                return HealthCheck(
                    check_name="Configuration Files",
                    status=HealthStatus.WARNING,
                    message=f"Missing configuration files: {', '.join(missing_files)}",
                    details={"missing_files": missing_files, "checked_files": config_files},
                    timestamp=datetime.now().isoformat(),
                    duration_ms=(time.time() - start_time) * 1000,
                    recommendations=[
                        "Create missing configuration files",
                        "Check if files were moved or renamed",
                        "Verify file paths are correct"
                    ]
                )
            else:
                return HealthCheck(
                    check_name="Configuration Files",
                    status=HealthStatus.HEALTHY,
                    message="All configuration files are present",
                    details={"config_files": config_files},
                    timestamp=datetime.now().isoformat(),
                    duration_ms=(time.time() - start_time) * 1000,
                    recommendations=[]
                )
                
        except Exception as e:
            return HealthCheck(
                check_name="Configuration Files",
                status=HealthStatus.CRITICAL,
                message=f"Error checking configuration: {str(e)}",
                details={"error": str(e)},
                timestamp=datetime.now().isoformat(),
                duration_ms=(time.time() - start_time) * 1000,
                recommendations=["Check file system permissions", "Verify working directory"]
            )
    
    async def _check_logging_system(self) -> HealthCheck:
        """Check logging system health"""
        start_time = time.time()
        
        try:
            # Test logging functionality
            test_logger = logger.bind(component="HealthCheck")
            test_logger.info("Health check logging test")
            
            # Check if logs directory exists
            logs_dir = Path("logs")
            if not logs_dir.exists():
                return HealthCheck(
                    check_name="Logging System",
                    status=HealthStatus.WARNING,
                    message="Logs directory does not exist",
                    details={"logs_directory": str(logs_dir)},
                    timestamp=datetime.now().isoformat(),
                    duration_ms=(time.time() - start_time) * 1000,
                    recommendations=["Create logs directory", "Check logging configuration"]
                )
            
            return HealthCheck(
                check_name="Logging System",
                status=HealthStatus.HEALTHY,
                message="Logging system is functional",
                details={"logs_directory": str(logs_dir), "logger_available": True},
                timestamp=datetime.now().isoformat(),
                duration_ms=(time.time() - start_time) * 1000,
                recommendations=[]
            )
            
        except Exception as e:
            return HealthCheck(
                check_name="Logging System",
                status=HealthStatus.CRITICAL,
                message=f"Logging system error: {str(e)}",
                details={"error": str(e)},
                timestamp=datetime.now().isoformat(),
                duration_ms=(time.time() - start_time) * 1000,
                recommendations=["Check loguru installation", "Verify logging configuration"]
            )

    async def _check_event_loop(self) -> HealthCheck:
        """Check asyncio event loop health"""
        start_time = time.time()

        try:
            # Test event loop functionality
            loop = asyncio.get_event_loop()

            # Test timer functionality
            timer_worked = False
            def timer_callback():
                nonlocal timer_worked
                timer_worked = True

            loop.call_later(0.1, timer_callback)
            await asyncio.sleep(0.2)

            if timer_worked:
                return HealthCheck(
                    check_name="Event Loop",
                    status=HealthStatus.HEALTHY,
                    message="Event loop is processing events correctly",
                    details={"loop_running": True, "timer_test": "passed"},
                    timestamp=datetime.now().isoformat(),
                    duration_ms=(time.time() - start_time) * 1000,
                    recommendations=[]
                )
            else:
                return HealthCheck(
                    check_name="Event Loop",
                    status=HealthStatus.CRITICAL,
                    message="Event loop is not processing events properly",
                    details={"loop_running": True, "timer_test": "failed"},
                    timestamp=datetime.now().isoformat(),
                    duration_ms=(time.time() - start_time) * 1000,
                    recommendations=[
                        "Check for blocking operations in main thread",
                        "Verify event loop integration",
                        "Check qasync installation"
                    ]
                )

        except Exception as e:
            return HealthCheck(
                check_name="Event Loop",
                status=HealthStatus.CRITICAL,
                message=f"Event loop error: {str(e)}",
                details={"error": str(e)},
                timestamp=datetime.now().isoformat(),
                duration_ms=(time.time() - start_time) * 1000,
                recommendations=["Check asyncio setup", "Verify event loop configuration"]
            )

    async def _check_database_file(self) -> HealthCheck:
        """Check database file existence and accessibility"""
        start_time = time.time()

        try:
            db_path = Path("kate.db")

            if not db_path.exists():
                return HealthCheck(
                    check_name="Database File",
                    status=HealthStatus.WARNING,
                    message="Database file does not exist (will be created on first run)",
                    details={"database_path": str(db_path), "exists": False},
                    timestamp=datetime.now().isoformat(),
                    duration_ms=(time.time() - start_time) * 1000,
                    recommendations=["Database will be created automatically on first startup"]
                )

            # Check file permissions
            if not db_path.is_file():
                return HealthCheck(
                    check_name="Database File",
                    status=HealthStatus.CRITICAL,
                    message="Database path exists but is not a file",
                    details={"database_path": str(db_path), "is_file": False},
                    timestamp=datetime.now().isoformat(),
                    duration_ms=(time.time() - start_time) * 1000,
                    recommendations=["Remove conflicting directory", "Check file permissions"]
                )

            # Check read/write permissions
            if not (db_path.stat().st_mode & 0o600):
                return HealthCheck(
                    check_name="Database File",
                    status=HealthStatus.WARNING,
                    message="Database file permissions may be incorrect",
                    details={"database_path": str(db_path), "permissions": oct(db_path.stat().st_mode)},
                    timestamp=datetime.now().isoformat(),
                    duration_ms=(time.time() - start_time) * 1000,
                    recommendations=["Check file permissions", "Ensure read/write access"]
                )

            return HealthCheck(
                check_name="Database File",
                status=HealthStatus.HEALTHY,
                message="Database file is accessible",
                details={"database_path": str(db_path), "size_bytes": db_path.stat().st_size},
                timestamp=datetime.now().isoformat(),
                duration_ms=(time.time() - start_time) * 1000,
                recommendations=[]
            )

        except Exception as e:
            return HealthCheck(
                check_name="Database File",
                status=HealthStatus.CRITICAL,
                message=f"Database file check error: {str(e)}",
                details={"error": str(e)},
                timestamp=datetime.now().isoformat(),
                duration_ms=(time.time() - start_time) * 1000,
                recommendations=["Check file system permissions", "Verify working directory"]
            )

    async def _check_database_connection(self) -> HealthCheck:
        """Check database connection"""
        start_time = time.time()

        try:
            # Try to connect to database
            import sqlite3

            db_path = Path("kate.db")
            if not db_path.exists():
                return HealthCheck(
                    check_name="Database Connection",
                    status=HealthStatus.WARNING,
                    message="Cannot test connection - database file does not exist",
                    details={"database_path": str(db_path)},
                    timestamp=datetime.now().isoformat(),
                    duration_ms=(time.time() - start_time) * 1000,
                    recommendations=["Database will be created on first startup"]
                )

            # Test connection
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            conn.close()

            if result and result[0] == 1:
                return HealthCheck(
                    check_name="Database Connection",
                    status=HealthStatus.HEALTHY,
                    message="Database connection successful",
                    details={"connection_test": "passed"},
                    timestamp=datetime.now().isoformat(),
                    duration_ms=(time.time() - start_time) * 1000,
                    recommendations=[]
                )
            else:
                return HealthCheck(
                    check_name="Database Connection",
                    status=HealthStatus.CRITICAL,
                    message="Database connection test failed",
                    details={"connection_test": "failed", "result": result},
                    timestamp=datetime.now().isoformat(),
                    duration_ms=(time.time() - start_time) * 1000,
                    recommendations=["Check database file integrity", "Verify SQLite installation"]
                )

        except Exception as e:
            return HealthCheck(
                check_name="Database Connection",
                status=HealthStatus.CRITICAL,
                message=f"Database connection error: {str(e)}",
                details={"error": str(e)},
                timestamp=datetime.now().isoformat(),
                duration_ms=(time.time() - start_time) * 1000,
                recommendations=["Check SQLite installation", "Verify database file permissions"]
            )

    async def _check_database_schema(self) -> HealthCheck:
        """Check database schema"""
        start_time = time.time()

        try:
            import sqlite3

            db_path = Path("kate.db")
            if not db_path.exists():
                return HealthCheck(
                    check_name="Database Schema",
                    status=HealthStatus.WARNING,
                    message="Cannot check schema - database file does not exist",
                    details={"database_path": str(db_path)},
                    timestamp=datetime.now().isoformat(),
                    duration_ms=(time.time() - start_time) * 1000,
                    recommendations=["Schema will be created on first startup"]
                )

            # Check for expected tables
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            conn.close()

            expected_tables = ["conversations", "messages", "documents", "chunks"]
            missing_tables = [table for table in expected_tables if table not in tables]

            if missing_tables:
                return HealthCheck(
                    check_name="Database Schema",
                    status=HealthStatus.WARNING,
                    message=f"Missing database tables: {', '.join(missing_tables)}",
                    details={"existing_tables": tables, "missing_tables": missing_tables},
                    timestamp=datetime.now().isoformat(),
                    duration_ms=(time.time() - start_time) * 1000,
                    recommendations=["Run database migrations", "Restart application to create tables"]
                )
            else:
                return HealthCheck(
                    check_name="Database Schema",
                    status=HealthStatus.HEALTHY,
                    message="Database schema is complete",
                    details={"tables": tables},
                    timestamp=datetime.now().isoformat(),
                    duration_ms=(time.time() - start_time) * 1000,
                    recommendations=[]
                )

        except Exception as e:
            return HealthCheck(
                check_name="Database Schema",
                status=HealthStatus.CRITICAL,
                message=f"Database schema check error: {str(e)}",
                details={"error": str(e)},
                timestamp=datetime.now().isoformat(),
                duration_ms=(time.time() - start_time) * 1000,
                recommendations=["Check database file integrity", "Verify SQLite installation"]
            )

    # Placeholder implementations for remaining check methods
    async def _check_qt_availability(self) -> HealthCheck:
        """Check Qt availability"""
        start_time = time.time()
        try:
            from PySide6.QtWidgets import QApplication
            return HealthCheck(
                check_name="Qt Availability",
                status=HealthStatus.HEALTHY,
                message="Qt/PySide6 is available",
                details={"qt_available": True},
                timestamp=datetime.now().isoformat(),
                duration_ms=(time.time() - start_time) * 1000,
                recommendations=[]
            )
        except ImportError as e:
            return HealthCheck(
                check_name="Qt Availability",
                status=HealthStatus.CRITICAL,
                message=f"Qt/PySide6 not available: {str(e)}",
                details={"error": str(e)},
                timestamp=datetime.now().isoformat(),
                duration_ms=(time.time() - start_time) * 1000,
                recommendations=["Install PySide6", "Check virtual environment"]
            )

    async def _check_display_environment(self) -> HealthCheck:
        """Check display environment"""
        start_time = time.time()
        import os

        display = os.environ.get("DISPLAY")
        if not display:
            return HealthCheck(
                check_name="Display Environment",
                status=HealthStatus.WARNING,
                message="No DISPLAY environment variable (headless environment)",
                details={"display": display, "headless": True},
                timestamp=datetime.now().isoformat(),
                duration_ms=(time.time() - start_time) * 1000,
                recommendations=["UI may not work in headless environment"]
            )

        return HealthCheck(
            check_name="Display Environment",
            status=HealthStatus.HEALTHY,
            message=f"Display environment available: {display}",
            details={"display": display, "headless": False},
            timestamp=datetime.now().isoformat(),
            duration_ms=(time.time() - start_time) * 1000,
            recommendations=[]
        )

    async def _check_ui_components(self) -> HealthCheck:
        """Check UI components"""
        start_time = time.time()

        try:
            # Check if UI module files exist
            ui_files = [
                "app/ui/main_window.py",
                "app/ui/components/chat_area.py",
                "app/ui/components/conversation_sidebar.py"
            ]

            missing_files = [f for f in ui_files if not Path(f).exists()]

            if missing_files:
                return HealthCheck(
                    check_name="UI Components",
                    status=HealthStatus.CRITICAL,
                    message=f"Missing UI files: {', '.join(missing_files)}",
                    details={"missing_files": missing_files},
                    timestamp=datetime.now().isoformat(),
                    duration_ms=(time.time() - start_time) * 1000,
                    recommendations=["Check if UI files were moved or deleted"]
                )

            return HealthCheck(
                check_name="UI Components",
                status=HealthStatus.HEALTHY,
                message="All UI component files are present",
                details={"ui_files": ui_files},
                timestamp=datetime.now().isoformat(),
                duration_ms=(time.time() - start_time) * 1000,
                recommendations=[]
            )

        except Exception as e:
            return HealthCheck(
                check_name="UI Components",
                status=HealthStatus.CRITICAL,
                message=f"UI components check error: {str(e)}",
                details={"error": str(e)},
                timestamp=datetime.now().isoformat(),
                duration_ms=(time.time() - start_time) * 1000,
                recommendations=["Check file system permissions"]
            )

    async def _check_ollama_service(self) -> HealthCheck:
        """Check Ollama service"""
        start_time = time.time()

        try:
            # Try to connect to Ollama
            import httpx

            async with httpx.AsyncClient() as client:
                try:
                    response = await client.get("http://localhost:11434/api/tags", timeout=5.0)
                    if response.status_code == 200:
                        data = response.json()
                        models = data.get("models", [])
                        return HealthCheck(
                            check_name="Ollama Service",
                            status=HealthStatus.HEALTHY,
                            message=f"Ollama is running with {len(models)} models",
                            details={"models_count": len(models), "status_code": response.status_code},
                            timestamp=datetime.now().isoformat(),
                            duration_ms=(time.time() - start_time) * 1000,
                            recommendations=[]
                        )
                    else:
                        return HealthCheck(
                            check_name="Ollama Service",
                            status=HealthStatus.WARNING,
                            message=f"Ollama responded with status {response.status_code}",
                            details={"status_code": response.status_code},
                            timestamp=datetime.now().isoformat(),
                            duration_ms=(time.time() - start_time) * 1000,
                            recommendations=["Check Ollama service status"]
                        )
                except httpx.TimeoutException:
                    return HealthCheck(
                        check_name="Ollama Service",
                        status=HealthStatus.CRITICAL,
                        message="Ollama service timeout",
                        details={"timeout": True},
                        timestamp=datetime.now().isoformat(),
                        duration_ms=(time.time() - start_time) * 1000,
                        recommendations=["Start Ollama service", "Check if Ollama is installed"]
                    )
                except httpx.ConnectError:
                    return HealthCheck(
                        check_name="Ollama Service",
                        status=HealthStatus.CRITICAL,
                        message="Cannot connect to Ollama service",
                        details={"connection_error": True},
                        timestamp=datetime.now().isoformat(),
                        duration_ms=(time.time() - start_time) * 1000,
                        recommendations=["Start Ollama service", "Check if Ollama is installed"]
                    )

        except ImportError:
            return HealthCheck(
                check_name="Ollama Service",
                status=HealthStatus.WARNING,
                message="httpx not available for Ollama check",
                details={"httpx_available": False},
                timestamp=datetime.now().isoformat(),
                duration_ms=(time.time() - start_time) * 1000,
                recommendations=["Install httpx dependency"]
            )
        except Exception as e:
            return HealthCheck(
                check_name="Ollama Service",
                status=HealthStatus.CRITICAL,
                message=f"Ollama check error: {str(e)}",
                details={"error": str(e)},
                timestamp=datetime.now().isoformat(),
                duration_ms=(time.time() - start_time) * 1000,
                recommendations=["Check Ollama installation and configuration"]
            )

    # Simplified implementations for remaining methods
    async def _check_embedding_service(self) -> HealthCheck:
        """Check embedding service"""
        return HealthCheck(
            check_name="Embedding Service",
            status=HealthStatus.HEALTHY,
            message="Embedding service check not implemented",
            details={},
            timestamp=datetime.now().isoformat(),
            duration_ms=0.0,
            recommendations=[]
        )

    async def _check_vector_store(self) -> HealthCheck:
        """Check vector store"""
        return HealthCheck(
            check_name="Vector Store",
            status=HealthStatus.HEALTHY,
            message="Vector store check not implemented",
            details={},
            timestamp=datetime.now().isoformat(),
            duration_ms=0.0,
            recommendations=[]
        )

    async def _check_python_version(self) -> HealthCheck:
        """Check Python version"""
        start_time = time.time()

        version = sys.version_info
        if version.major == 3 and version.minor >= 11:
            status = HealthStatus.HEALTHY
            message = f"Python {version.major}.{version.minor}.{version.micro} is supported"
        elif version.major == 3 and version.minor >= 8:
            status = HealthStatus.WARNING
            message = f"Python {version.major}.{version.minor}.{version.micro} may work but 3.11+ recommended"
        else:
            status = HealthStatus.CRITICAL
            message = f"Python {version.major}.{version.minor}.{version.micro} is not supported"

        return HealthCheck(
            check_name="Python Version",
            status=status,
            message=message,
            details={"version": f"{version.major}.{version.minor}.{version.micro}"},
            timestamp=datetime.now().isoformat(),
            duration_ms=(time.time() - start_time) * 1000,
            recommendations=["Upgrade to Python 3.11+" if status != HealthStatus.HEALTHY else []]
        )

    async def _check_required_packages(self) -> HealthCheck:
        """Check required packages"""
        start_time = time.time()

        required_packages = [
            "PySide6", "loguru", "sqlalchemy", "aiosqlite",
            "pydantic", "httpx", "qasync"
        ]

        missing_packages = []
        for package in required_packages:
            try:
                __import__(package.lower().replace("-", "_"))
            except ImportError:
                missing_packages.append(package)

        if missing_packages:
            return HealthCheck(
                check_name="Required Packages",
                status=HealthStatus.CRITICAL,
                message=f"Missing packages: {', '.join(missing_packages)}",
                details={"missing_packages": missing_packages, "required_packages": required_packages},
                timestamp=datetime.now().isoformat(),
                duration_ms=(time.time() - start_time) * 1000,
                recommendations=[f"Install missing packages: pip install {' '.join(missing_packages)}"]
            )

        return HealthCheck(
            check_name="Required Packages",
            status=HealthStatus.HEALTHY,
            message="All required packages are available",
            details={"required_packages": required_packages},
            timestamp=datetime.now().isoformat(),
            duration_ms=(time.time() - start_time) * 1000,
            recommendations=[]
        )

    async def _check_virtual_environment(self) -> HealthCheck:
        """Check virtual environment"""
        start_time = time.time()

        in_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)

        if in_venv:
            return HealthCheck(
                check_name="Virtual Environment",
                status=HealthStatus.HEALTHY,
                message="Running in virtual environment",
                details={"in_venv": True, "prefix": sys.prefix},
                timestamp=datetime.now().isoformat(),
                duration_ms=(time.time() - start_time) * 1000,
                recommendations=[]
            )
        else:
            return HealthCheck(
                check_name="Virtual Environment",
                status=HealthStatus.WARNING,
                message="Not running in virtual environment",
                details={"in_venv": False, "prefix": sys.prefix},
                timestamp=datetime.now().isoformat(),
                duration_ms=(time.time() - start_time) * 1000,
                recommendations=["Consider using virtual environment for dependency isolation"]
            )

    async def _check_disk_space(self) -> HealthCheck:
        """Check disk space"""
        start_time = time.time()

        try:
            import shutil

            total, used, free = shutil.disk_usage(".")
            free_gb = free / (1024**3)

            if free_gb < 1.0:
                status = HealthStatus.CRITICAL
                message = f"Low disk space: {free_gb:.1f}GB free"
                recommendations = ["Free up disk space"]
            elif free_gb < 5.0:
                status = HealthStatus.WARNING
                message = f"Limited disk space: {free_gb:.1f}GB free"
                recommendations = ["Monitor disk space usage"]
            else:
                status = HealthStatus.HEALTHY
                message = f"Sufficient disk space: {free_gb:.1f}GB free"
                recommendations = []

            return HealthCheck(
                check_name="Disk Space",
                status=status,
                message=message,
                details={"free_gb": free_gb, "total_gb": total / (1024**3)},
                timestamp=datetime.now().isoformat(),
                duration_ms=(time.time() - start_time) * 1000,
                recommendations=recommendations
            )

        except Exception as e:
            return HealthCheck(
                check_name="Disk Space",
                status=HealthStatus.WARNING,
                message=f"Could not check disk space: {str(e)}",
                details={"error": str(e)},
                timestamp=datetime.now().isoformat(),
                duration_ms=(time.time() - start_time) * 1000,
                recommendations=["Check file system permissions"]
            )

    async def _check_file_permissions(self) -> HealthCheck:
        """Check file permissions"""
        start_time = time.time()

        try:
            # Test write permissions in current directory
            test_file = Path("health_check_test.tmp")
            test_file.write_text("test")
            test_file.unlink()

            return HealthCheck(
                check_name="File Permissions",
                status=HealthStatus.HEALTHY,
                message="File system permissions are adequate",
                details={"write_test": "passed"},
                timestamp=datetime.now().isoformat(),
                duration_ms=(time.time() - start_time) * 1000,
                recommendations=[]
            )

        except Exception as e:
            return HealthCheck(
                check_name="File Permissions",
                status=HealthStatus.CRITICAL,
                message=f"File permission error: {str(e)}",
                details={"error": str(e)},
                timestamp=datetime.now().isoformat(),
                duration_ms=(time.time() - start_time) * 1000,
                recommendations=["Check directory permissions", "Verify write access"]
            )

    async def _check_required_directories(self) -> HealthCheck:
        """Check required directories"""
        start_time = time.time()

        required_dirs = ["app", "debug", "ai_workspace"]
        missing_dirs = [d for d in required_dirs if not Path(d).exists()]

        if missing_dirs:
            return HealthCheck(
                check_name="Required Directories",
                status=HealthStatus.WARNING,
                message=f"Missing directories: {', '.join(missing_dirs)}",
                details={"missing_dirs": missing_dirs, "required_dirs": required_dirs},
                timestamp=datetime.now().isoformat(),
                duration_ms=(time.time() - start_time) * 1000,
                recommendations=["Create missing directories"]
            )

        return HealthCheck(
            check_name="Required Directories",
            status=HealthStatus.HEALTHY,
            message="All required directories are present",
            details={"required_dirs": required_dirs},
            timestamp=datetime.now().isoformat(),
            duration_ms=(time.time() - start_time) * 1000,
            recommendations=[]
        )

    def _create_component_health(self, component_name: str, checks: List[HealthCheck],
                               dependencies: List[str]) -> ComponentHealth:
        """Create component health summary"""

        # Determine overall status
        statuses = [check.status for check in checks]
        if HealthStatus.CRITICAL in statuses:
            overall_status = HealthStatus.CRITICAL
        elif HealthStatus.WARNING in statuses:
            overall_status = HealthStatus.WARNING
        else:
            overall_status = HealthStatus.HEALTHY

        # Extract issues
        critical_issues = [check.message for check in checks if check.status == HealthStatus.CRITICAL]
        warnings = [check.message for check in checks if check.status == HealthStatus.WARNING]

        return ComponentHealth(
            component_name=component_name,
            overall_status=overall_status,
            checks=checks,
            dependencies=dependencies,
            critical_issues=critical_issues,
            warnings=warnings
        )

    def _determine_overall_status(self, components: List[ComponentHealth]) -> HealthStatus:
        """Determine overall system status"""
        statuses = [component.overall_status for component in components]

        if HealthStatus.CRITICAL in statuses:
            return HealthStatus.CRITICAL
        elif HealthStatus.WARNING in statuses:
            return HealthStatus.WARNING
        else:
            return HealthStatus.HEALTHY

    def _generate_health_summary(self, components: List[ComponentHealth]) -> Dict[str, Any]:
        """Generate health summary statistics"""
        total_checks = sum(len(component.checks) for component in components)

        status_counts = {
            "healthy": 0,
            "warning": 0,
            "critical": 0,
            "unknown": 0
        }

        for component in components:
            for check in component.checks:
                status_counts[check.status.value] += 1

        return {
            "total_components": len(components),
            "total_checks": total_checks,
            "status_breakdown": status_counts,
            "health_percentage": (status_counts["healthy"] / total_checks * 100) if total_checks > 0 else 0
        }

    def _generate_health_recommendations(self, components: List[ComponentHealth]) -> List[str]:
        """Generate overall health recommendations"""
        recommendations = []

        for component in components:
            if component.critical_issues:
                recommendations.append(f"CRITICAL: Fix {component.component_name} issues")

            for check in component.checks:
                if check.status in [HealthStatus.CRITICAL, HealthStatus.WARNING]:
                    recommendations.extend(check.recommendations)

        # Remove duplicates while preserving order
        seen = set()
        unique_recommendations = []
        for rec in recommendations:
            if rec not in seen:
                seen.add(rec)
                unique_recommendations.append(rec)

        return unique_recommendations[:10]  # Limit to top 10 recommendations

    def generate_health_report_text(self, report: SystemHealthReport) -> str:
        """Generate human-readable health report"""

        lines = [
            "# Kate Application Health Report",
            f"Generated: {report.timestamp}",
            f"Overall Status: {report.overall_status.value.upper()}",
            "",
            "## Summary",
            f"- Components Checked: {report.summary['total_components']}",
            f"- Total Health Checks: {report.summary['total_checks']}",
            f"- Health Percentage: {report.summary['health_percentage']:.1f}%",
            "",
            "## Component Status"
        ]

        for component in report.components:
            status_icon = {
                HealthStatus.HEALTHY: "✅",
                HealthStatus.WARNING: "⚠️",
                HealthStatus.CRITICAL: "❌",
                HealthStatus.UNKNOWN: "❓"
            }[component.overall_status]

            lines.append(f"### {status_icon} {component.component_name}")
            lines.append(f"Status: {component.overall_status.value.upper()}")

            if component.critical_issues:
                lines.append("**Critical Issues:**")
                for issue in component.critical_issues:
                    lines.append(f"- {issue}")

            if component.warnings:
                lines.append("**Warnings:**")
                for warning in component.warnings:
                    lines.append(f"- {warning}")

            lines.append("")

        if report.recommendations:
            lines.extend([
                "## Recommendations",
                ""
            ])
            for i, rec in enumerate(report.recommendations, 1):
                lines.append(f"{i}. {rec}")

        return "\n".join(lines)

    def save_health_report(self, report: SystemHealthReport, filepath: Path) -> None:
        """Save health report to file"""

        report_data = {
            "report": asdict(report),
            "text_report": self.generate_health_report_text(report)
        }

        with open(filepath, 'w') as f:
            json.dump(report_data, f, indent=2)

        self.logger.info(f"Health report saved to {filepath}")


# Global health checker instance
application_health_checker = ApplicationHealthChecker()


async def run_health_check() -> SystemHealthReport:
    """Convenience function for running health check"""
    return await application_health_checker.run_full_health_check()
