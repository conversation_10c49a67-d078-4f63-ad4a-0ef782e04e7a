"""
AI Testing Framework for Kate LLM Client.

Comprehensive testing framework designed specifically for AI debugging and validation.
Provides automated UI testing, service integration testing, and detailed reporting.
"""

import asyncio
import json
import time
from dataclasses import asdict, dataclass
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

from loguru import logger
from PySide6.QtCore import QObject, Qt, QThread, QTimer, Signal
from PySide6.QtTest import QTest
from PySide6.QtWidgets import QApplication, QWidget


class TestResult(Enum):
    """Test result status"""
    PASS = "PASS"
    FAIL = "FAIL"
    SKIP = "SKIP"
    ERROR = "ERROR"


@dataclass
class ComponentTestResult:
    """Result of testing a single component"""
    component_name: str
    test_name: str
    result: TestResult
    duration_ms: float
    details: str
    error_message: Optional[str] = None
    stack_trace: Optional[str] = None
    timestamp: str = ""
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.now().isoformat()


@dataclass
class InteractionTestResult:
    """Result of testing UI interactions"""
    widget_name: str
    interaction_type: str  # click, type, focus, etc.
    expected_result: str
    actual_result: str
    success: bool
    response_time_ms: float
    details: Dict[str, Any]


@dataclass
class WidgetInfo:
    """Information about a widget"""
    name: str
    class_name: str
    parent: Optional[str]
    visible: bool
    enabled: bool
    geometry: Dict[str, int]
    properties: Dict[str, Any]
    signals: List[str]
    slots: List[str]


class AITestingFramework:
    """
    Comprehensive testing framework designed for AI debugging.
    
    Provides automated testing of UI components, service integrations,
    and generates detailed reports for AI analysis.
    """
    
    def __init__(self, app_instance=None):
        self.app = app_instance or QApplication.instance()
        self.test_results: List[ComponentTestResult] = []
        self.interaction_results: List[InteractionTestResult] = []
        self.widget_hierarchy: Dict[str, WidgetInfo] = {}
        self.logger = logger.bind(component="AITestingFramework")
        
        # Test configuration
        self.timeout_ms = 5000
        self.interaction_delay_ms = 100
        
    def test_ui_component(self, component: QWidget, component_name: str) -> ComponentTestResult:
        """
        Test a UI component comprehensively.
        
        Args:
            component: The widget to test
            component_name: Human-readable name for the component
            
        Returns:
            ComponentTestResult with detailed test information
        """
        start_time = time.time()
        
        try:
            # Basic widget tests
            visibility_test = self._test_widget_visibility(component)
            interaction_test = self._test_widget_interaction(component)
            signal_test = self._test_widget_signals(component)
            
            # Combine results
            all_passed = all([visibility_test, interaction_test, signal_test])
            
            result = ComponentTestResult(
                component_name=component_name,
                test_name="comprehensive_ui_test",
                result=TestResult.PASS if all_passed else TestResult.FAIL,
                duration_ms=(time.time() - start_time) * 1000,
                details=f"Visibility: {visibility_test}, Interaction: {interaction_test}, Signals: {signal_test}"
            )
            
        except Exception as e:
            result = ComponentTestResult(
                component_name=component_name,
                test_name="comprehensive_ui_test",
                result=TestResult.ERROR,
                duration_ms=(time.time() - start_time) * 1000,
                details="Exception during testing",
                error_message=str(e),
                stack_trace=self._get_stack_trace()
            )
            
        self.test_results.append(result)
        return result
    
    def test_widget_interaction(self, widget: QWidget, interaction_type: str, 
                              expected_result: str = "") -> InteractionTestResult:
        """
        Test specific widget interaction.
        
        Args:
            widget: Widget to interact with
            interaction_type: Type of interaction (click, type, focus)
            expected_result: Expected outcome description
            
        Returns:
            InteractionTestResult with detailed interaction information
        """
        start_time = time.time()
        widget_name = widget.objectName() or widget.__class__.__name__
        
        try:
            if interaction_type == "click":
                result = self._test_click_interaction(widget)
            elif interaction_type == "type":
                result = self._test_type_interaction(widget)
            elif interaction_type == "focus":
                result = self._test_focus_interaction(widget)
            else:
                result = {"success": False, "details": f"Unknown interaction type: {interaction_type}"}
            
            interaction_result = InteractionTestResult(
                widget_name=widget_name,
                interaction_type=interaction_type,
                expected_result=expected_result,
                actual_result=result.get("details", ""),
                success=result.get("success", False),
                response_time_ms=(time.time() - start_time) * 1000,
                details=result
            )
            
        except Exception as e:
            interaction_result = InteractionTestResult(
                widget_name=widget_name,
                interaction_type=interaction_type,
                expected_result=expected_result,
                actual_result=f"Error: {str(e)}",
                success=False,
                response_time_ms=(time.time() - start_time) * 1000,
                details={"error": str(e), "stack_trace": self._get_stack_trace()}
            )
        
        self.interaction_results.append(interaction_result)
        return interaction_result
    
    def create_widget_hierarchy_map(self, root_widget: QWidget) -> Dict[str, WidgetInfo]:
        """
        Create a comprehensive map of all widgets and their relationships.
        
        Args:
            root_widget: Root widget to start mapping from
            
        Returns:
            Dictionary mapping widget names to WidgetInfo objects
        """
        self.widget_hierarchy = {}
        self._map_widget_recursive(root_widget, None)
        return self.widget_hierarchy
    
    def generate_interaction_report(self) -> str:
        """
        Generate a comprehensive human-readable interaction test report.
        
        Returns:
            Formatted report string suitable for AI analysis
        """
        report_lines = [
            "# AI Testing Framework - Interaction Report",
            f"Generated: {datetime.now().isoformat()}",
            f"Total Components Tested: {len(self.test_results)}",
            f"Total Interactions Tested: {len(self.interaction_results)}",
            "",
            "## Component Test Summary"
        ]
        
        # Component test summary
        passed = sum(1 for r in self.test_results if r.result == TestResult.PASS)
        failed = sum(1 for r in self.test_results if r.result == TestResult.FAIL)
        errors = sum(1 for r in self.test_results if r.result == TestResult.ERROR)
        
        report_lines.extend([
            f"- ✅ Passed: {passed}",
            f"- ❌ Failed: {failed}",
            f"- 🚨 Errors: {errors}",
            "",
            "## Detailed Results"
        ])
        
        # Detailed results
        for result in self.test_results:
            status_icon = "✅" if result.result == TestResult.PASS else "❌"
            report_lines.extend([
                f"### {status_icon} {result.component_name}",
                f"- Test: {result.test_name}",
                f"- Duration: {result.duration_ms:.1f}ms",
                f"- Details: {result.details}",
            ])
            
            if result.error_message:
                report_lines.append(f"- Error: {result.error_message}")
            
            report_lines.append("")
        
        # Interaction results
        if self.interaction_results:
            report_lines.extend([
                "## Interaction Test Results",
                ""
            ])
            
            for interaction in self.interaction_results:
                status_icon = "✅" if interaction.success else "❌"
                report_lines.extend([
                    f"### {status_icon} {interaction.widget_name} - {interaction.interaction_type}",
                    f"- Expected: {interaction.expected_result}",
                    f"- Actual: {interaction.actual_result}",
                    f"- Response Time: {interaction.response_time_ms:.1f}ms",
                    ""
                ])
        
        return "\n".join(report_lines)
    
    def save_test_results(self, filepath: Union[str, Path]) -> None:
        """Save test results to JSON file for later analysis"""
        filepath = Path(filepath)
        
        data = {
            "timestamp": datetime.now().isoformat(),
            "component_tests": [asdict(result) for result in self.test_results],
            "interaction_tests": [asdict(result) for result in self.interaction_results],
            "widget_hierarchy": {name: asdict(info) for name, info in self.widget_hierarchy.items()}
        }
        
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2)
        
        self.logger.info(f"Test results saved to {filepath}")
    
    # Private helper methods
    def _test_widget_visibility(self, widget: QWidget) -> bool:
        """Test if widget is visible and properly sized"""
        return widget.isVisible() and widget.size().width() > 0 and widget.size().height() > 0
    
    def _test_widget_interaction(self, widget: QWidget) -> bool:
        """Test basic widget interaction capabilities"""
        return widget.isEnabled() and not widget.visibleRegion().isEmpty()
    
    def _test_widget_signals(self, widget: QWidget) -> bool:
        """Test if widget has expected signals"""
        # Basic test - check if widget has any signals
        return hasattr(widget, 'metaObject')
    
    def _test_click_interaction(self, widget: QWidget) -> Dict[str, Any]:
        """Test clicking on a widget"""
        try:
            # Simulate click
            QTest.mouseClick(widget, Qt.LeftButton)
            QTest.qWait(self.interaction_delay_ms)
            
            return {
                "success": True,
                "details": "Click interaction completed",
                "widget_enabled": widget.isEnabled(),
                "widget_visible": widget.isVisible()
            }
        except Exception as e:
            return {
                "success": False,
                "details": f"Click failed: {str(e)}",
                "error": str(e)
            }
    
    def _test_type_interaction(self, widget: QWidget) -> Dict[str, Any]:
        """Test typing in a widget"""
        try:
            if hasattr(widget, 'setText'):
                test_text = "AI_TEST_INPUT"
                widget.setText(test_text)
                QTest.qWait(self.interaction_delay_ms)
                
                actual_text = widget.text() if hasattr(widget, 'text') else ""
                
                return {
                    "success": actual_text == test_text,
                    "details": f"Type test - Expected: '{test_text}', Got: '{actual_text}'",
                    "expected": test_text,
                    "actual": actual_text
                }
            else:
                return {
                    "success": False,
                    "details": "Widget does not support text input",
                    "widget_type": widget.__class__.__name__
                }
        except Exception as e:
            return {
                "success": False,
                "details": f"Type interaction failed: {str(e)}",
                "error": str(e)
            }
    
    def _test_focus_interaction(self, widget: QWidget) -> Dict[str, Any]:
        """Test focus interaction with widget"""
        try:
            widget.setFocus()
            QTest.qWait(self.interaction_delay_ms)
            
            has_focus = widget.hasFocus()
            
            return {
                "success": has_focus,
                "details": f"Focus test - Widget has focus: {has_focus}",
                "has_focus": has_focus,
                "focus_policy": widget.focusPolicy()
            }
        except Exception as e:
            return {
                "success": False,
                "details": f"Focus interaction failed: {str(e)}",
                "error": str(e)
            }
    
    def _map_widget_recursive(self, widget: QWidget, parent_name: Optional[str]) -> None:
        """Recursively map widget hierarchy"""
        widget_name = widget.objectName() or f"{widget.__class__.__name__}_{id(widget)}"
        
        # Get widget signals and slots
        meta_object = widget.metaObject()
        signals = []
        slots = []
        
        for i in range(meta_object.methodCount()):
            method = meta_object.method(i)
            if method.methodType() == method.Signal:
                signals.append(method.name().data().decode())
            elif method.methodType() == method.Slot:
                slots.append(method.name().data().decode())
        
        # Create widget info
        widget_info = WidgetInfo(
            name=widget_name,
            class_name=widget.__class__.__name__,
            parent=parent_name,
            visible=widget.isVisible(),
            enabled=widget.isEnabled(),
            geometry={
                "x": widget.x(),
                "y": widget.y(),
                "width": widget.width(),
                "height": widget.height()
            },
            properties={
                "object_name": widget.objectName(),
                "window_title": widget.windowTitle() if hasattr(widget, 'windowTitle') else "",
                "tool_tip": widget.toolTip(),
                "status_tip": widget.statusTip(),
                "focus_policy": str(widget.focusPolicy())
            },
            signals=signals[:10],  # Limit to first 10 signals
            slots=slots[:10]       # Limit to first 10 slots
        )
        
        self.widget_hierarchy[widget_name] = widget_info
        
        # Recursively map children
        for child in widget.findChildren(QWidget):
            if child.parent() == widget:  # Only direct children
                self._map_widget_recursive(child, widget_name)
    
    def _get_stack_trace(self) -> str:
        """Get current stack trace as string"""
        import traceback
        return traceback.format_exc()


# Convenience functions for AI usage
def test_main_window(main_window) -> str:
    """Quick test of main window - returns report string"""
    framework = AITestingFramework()
    framework.create_widget_hierarchy_map(main_window)
    framework.test_ui_component(main_window, "MainWindow")
    return framework.generate_interaction_report()


def quick_ui_health_check(root_widget) -> Dict[str, Any]:
    """Quick health check returning simple pass/fail status"""
    framework = AITestingFramework()
    result = framework.test_ui_component(root_widget, "QuickHealthCheck")

    return {
        "healthy": result.result == TestResult.PASS,
        "details": result.details,
        "error": result.error_message,
        "duration_ms": result.duration_ms
    }


def comprehensive_kate_test() -> Dict[str, Any]:
    """Comprehensive test of Kate application for AI validation"""
    try:
        import asyncio

        from app.core.application import KateApplication
        from app.main import setup_platform, setup_qt_application

        # Setup
        setup_platform()
        kate_app = KateApplication()

        # Run async startup
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(kate_app.startup())

        # Test main window
        main_window = kate_app.get_main_window()
        if not main_window:
            return {"success": False, "error": "Main window not found"}

        # Run comprehensive tests
        framework = AITestingFramework()
        framework.create_widget_hierarchy_map(main_window)
        result = framework.test_ui_component(main_window, "KateApplication")

        # Test specific Kate components
        chat_test = framework.test_widget_interaction(main_window, "click", "Chat functionality")

        # Cleanup
        loop.run_until_complete(kate_app.shutdown())
        loop.close()

        return {
            "success": result.result == TestResult.PASS,
            "main_test": result,
            "chat_test": chat_test,
            "report": framework.generate_interaction_report()
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "traceback": framework._get_stack_trace() if 'framework' in locals() else ""
        }
