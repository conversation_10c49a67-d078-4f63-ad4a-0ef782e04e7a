"""
AI Guidance Framework for Kate LLM Client.

Provides intelligent monitoring, decision support, and guidance systems
to help AI agents make better development decisions and avoid common pitfalls.
"""

import json
import time
from dataclasses import asdict, dataclass
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

from loguru import logger


class GuidanceLevel(Enum):
    """Level of guidance intervention needed"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    BLOCKING = "blocking"


class DecisionCategory(Enum):
    """Categories of decisions that need guidance"""
    ARCHITECTURE = "architecture"
    CODE_CHANGE = "code_change"
    TESTING = "testing"
    DEBUGGING = "debugging"
    DEPENDENCY = "dependency"
    PERFORMANCE = "performance"
    SECURITY = "security"


@dataclass
class GuidanceRule:
    """Rule for providing guidance to AI agents"""
    rule_id: str
    category: DecisionCategory
    condition: str  # Description of when this rule applies
    guidance_level: GuidanceLevel
    message: str
    suggested_actions: List[str]
    blocking_conditions: List[str]  # Conditions that should block the action
    confidence: float  # 0.0 to 1.0


@dataclass
class DecisionContext:
    """Context information for a decision being made"""
    decision_id: str
    timestamp: str
    agent_id: str
    category: DecisionCategory
    description: str
    proposed_action: str
    files_affected: List[str]
    risk_level: str  # "low", "medium", "high"
    context_data: Dict[str, Any]


@dataclass
class GuidanceResponse:
    """Response from the guidance system"""
    decision_id: str
    guidance_level: GuidanceLevel
    should_proceed: bool
    message: str
    suggested_actions: List[str]
    alternative_approaches: List[str]
    required_validations: List[str]
    estimated_risk: str
    confidence: float


@dataclass
class AIAgentProfile:
    """Profile of an AI agent for personalized guidance"""
    agent_id: str
    agent_type: str  # "developer", "reviewer", "tester"
    experience_level: str  # "novice", "intermediate", "expert"
    success_patterns: List[str]
    failure_patterns: List[str]
    preferred_approaches: List[str]
    areas_of_expertise: List[str]
    common_mistakes: List[str]


class AIGuidanceFramework:
    """
    Comprehensive guidance framework for AI development.
    
    Provides intelligent monitoring, decision support, and guidance
    to help AI agents make better development decisions.
    """
    
    def __init__(self):
        self.guidance_rules = self._load_guidance_rules()
        self.agent_profiles: Dict[str, AIAgentProfile] = {}
        self.decision_history: List[DecisionContext] = []
        self.guidance_history: List[GuidanceResponse] = []
        self.logger = logger.bind(component="AIGuidanceFramework")
        
        # Load existing agent profiles
        self._load_agent_profiles()
    
    def register_agent(self, agent_profile: AIAgentProfile) -> None:
        """Register an AI agent with the guidance system"""
        self.agent_profiles[agent_profile.agent_id] = agent_profile
        self.logger.info(f"Registered AI agent: {agent_profile.agent_id} ({agent_profile.agent_type})")
    
    def request_guidance(self, decision_context: DecisionContext) -> GuidanceResponse:
        """
        Request guidance for a specific decision.
        
        Args:
            decision_context: Context information about the decision
            
        Returns:
            GuidanceResponse with recommendations and guidance
        """
        self.decision_history.append(decision_context)
        
        # Analyze the decision against guidance rules
        applicable_rules = self._find_applicable_rules(decision_context)
        
        # Get agent-specific guidance
        agent_guidance = self._get_agent_specific_guidance(decision_context)
        
        # Analyze historical patterns
        pattern_analysis = self._analyze_decision_patterns(decision_context)
        
        # Generate comprehensive guidance response
        guidance = self._generate_guidance_response(
            decision_context, applicable_rules, agent_guidance, pattern_analysis
        )
        
        self.guidance_history.append(guidance)
        
        # Log the guidance provided
        self.logger.info(
            f"Provided {guidance.guidance_level.value} guidance for {decision_context.category.value}: "
            f"{'PROCEED' if guidance.should_proceed else 'BLOCK'}"
        )
        
        return guidance
    
    def evaluate_code_change(self, file_path: str, change_description: str, 
                           agent_id: str) -> GuidanceResponse:
        """Evaluate a proposed code change and provide guidance"""
        
        decision_context = DecisionContext(
            decision_id=f"code_change_{int(time.time())}",
            timestamp=datetime.now().isoformat(),
            agent_id=agent_id,
            category=DecisionCategory.CODE_CHANGE,
            description=f"Code change in {file_path}: {change_description}",
            proposed_action=change_description,
            files_affected=[file_path],
            risk_level=self._assess_change_risk(file_path, change_description),
            context_data={
                "file_path": file_path,
                "change_type": self._classify_change_type(change_description)
            }
        )
        
        return self.request_guidance(decision_context)
    
    def monitor_agent_progress(self, agent_id: str) -> Dict[str, Any]:
        """Monitor an AI agent's progress and provide insights"""
        
        if agent_id not in self.agent_profiles:
            return {"error": f"Agent {agent_id} not registered"}
        
        # Get recent decisions by this agent
        recent_decisions = [
            d for d in self.decision_history[-20:] 
            if d.agent_id == agent_id
        ]
        
        # Get recent guidance responses
        recent_guidance = [
            g for g in self.guidance_history[-20:]
            if any(d.agent_id == agent_id and d.decision_id == g.decision_id 
                  for d in recent_decisions)
        ]
        
        # Analyze patterns
        success_rate = self._calculate_success_rate(agent_id)
        common_issues = self._identify_common_issues(agent_id)
        improvement_areas = self._suggest_improvements(agent_id)
        
        return {
            "agent_id": agent_id,
            "recent_activity": {
                "decisions_made": len(recent_decisions),
                "guidance_received": len(recent_guidance),
                "blocked_actions": len([g for g in recent_guidance if not g.should_proceed])
            },
            "performance_metrics": {
                "success_rate": success_rate,
                "average_risk_level": self._calculate_average_risk(agent_id),
                "guidance_adherence": self._calculate_guidance_adherence(agent_id)
            },
            "insights": {
                "common_issues": common_issues,
                "improvement_areas": improvement_areas,
                "strengths": self._identify_strengths(agent_id)
            }
        }
    
    def generate_session_report(self, session_id: str = None) -> str:
        """Generate a comprehensive session report"""
        
        # Get session data (last hour if no session_id provided)
        if session_id is None:
            cutoff_time = datetime.now().timestamp() - 3600  # Last hour
            session_decisions = [
                d for d in self.decision_history
                if datetime.fromisoformat(d.timestamp).timestamp() > cutoff_time
            ]
            session_guidance = [
                g for g in self.guidance_history
                if any(d.decision_id == g.decision_id for d in session_decisions)
            ]
        else:
            # Filter by session_id if provided
            session_decisions = [d for d in self.decision_history if session_id in d.context_data.get("session_id", "")]
            session_guidance = [g for g in self.guidance_history if session_id in g.decision_id]
        
        # Generate report
        report_lines = [
            "# AI Guidance Framework - Session Report",
            f"Generated: {datetime.now().isoformat()}",
            f"Session ID: {session_id or 'Last Hour'}",
            "",
            "## Summary",
            f"- Total Decisions: {len(session_decisions)}",
            f"- Guidance Provided: {len(session_guidance)}",
            f"- Blocked Actions: {len([g for g in session_guidance if not g.should_proceed])}",
            f"- Critical Issues: {len([g for g in session_guidance if g.guidance_level == GuidanceLevel.CRITICAL])}",
            "",
            "## Decision Breakdown by Category"
        ]
        
        # Category breakdown
        category_counts = {}
        for decision in session_decisions:
            category_counts[decision.category.value] = category_counts.get(decision.category.value, 0) + 1
        
        for category, count in category_counts.items():
            report_lines.append(f"- {category.title()}: {count}")
        
        report_lines.extend([
            "",
            "## Key Guidance Provided"
        ])
        
        # Important guidance
        critical_guidance = [g for g in session_guidance if g.guidance_level in [GuidanceLevel.CRITICAL, GuidanceLevel.BLOCKING]]
        for guidance in critical_guidance:
            decision = next((d for d in session_decisions if d.decision_id == guidance.decision_id), None)
            if decision:
                report_lines.extend([
                    f"### {guidance.guidance_level.value.upper()}: {decision.description}",
                    f"- **Action:** {'ALLOWED' if guidance.should_proceed else 'BLOCKED'}",
                    f"- **Message:** {guidance.message}",
                    f"- **Risk Level:** {guidance.estimated_risk}",
                    ""
                ])
        
        return "\n".join(report_lines)
    
    def _load_guidance_rules(self) -> List[GuidanceRule]:
        """Load guidance rules for Kate development"""
        return [
            GuidanceRule(
                rule_id="critical_file_modification",
                category=DecisionCategory.CODE_CHANGE,
                condition="Modifying core application files",
                guidance_level=GuidanceLevel.WARNING,
                message="You're modifying a critical application file. Ensure thorough testing.",
                suggested_actions=[
                    "Run comprehensive tests after changes",
                    "Create backup commit before modification",
                    "Verify no breaking changes to public APIs"
                ],
                blocking_conditions=[
                    "No tests available for this component",
                    "Previous similar changes caused failures"
                ],
                confidence=0.9
            ),
            GuidanceRule(
                rule_id="event_loop_modification",
                category=DecisionCategory.CODE_CHANGE,
                condition="Modifying Qt event loop or asyncio integration",
                guidance_level=GuidanceLevel.CRITICAL,
                message="Event loop changes are high-risk. This affects UI responsiveness.",
                suggested_actions=[
                    "Test UI responsiveness thoroughly",
                    "Run emergency_ui_test.py",
                    "Verify no blocking operations in main thread"
                ],
                blocking_conditions=[
                    "No UI testing framework available",
                    "Previous event loop changes caused UI freezing"
                ],
                confidence=0.95
            ),
            GuidanceRule(
                rule_id="dependency_addition",
                category=DecisionCategory.DEPENDENCY,
                condition="Adding new dependencies",
                guidance_level=GuidanceLevel.WARNING,
                message="Adding dependencies increases complexity and potential conflicts.",
                suggested_actions=[
                    "Check for existing alternatives in codebase",
                    "Verify license compatibility",
                    "Update requirements documentation"
                ],
                blocking_conditions=[
                    "Dependency has known security vulnerabilities",
                    "License incompatible with project"
                ],
                confidence=0.8
            ),
            GuidanceRule(
                rule_id="database_schema_change",
                category=DecisionCategory.CODE_CHANGE,
                condition="Modifying database models or schema",
                guidance_level=GuidanceLevel.WARNING,
                message="Database changes require migration planning.",
                suggested_actions=[
                    "Create database migration script",
                    "Test with existing data",
                    "Plan rollback strategy"
                ],
                blocking_conditions=[
                    "No migration system in place",
                    "Changes would cause data loss"
                ],
                confidence=0.85
            ),
            GuidanceRule(
                rule_id="ui_component_modification",
                category=DecisionCategory.CODE_CHANGE,
                condition="Modifying UI components or layouts",
                guidance_level=GuidanceLevel.INFO,
                message="UI changes should maintain consistency and usability.",
                suggested_actions=[
                    "Test across different screen sizes",
                    "Verify accessibility compliance",
                    "Check theme compatibility"
                ],
                blocking_conditions=[],
                confidence=0.7
            )
        ]
    
    def _load_agent_profiles(self) -> None:
        """Load existing agent profiles"""
        profiles_file = Path("ai_workspace/agent_profiles.json")
        if profiles_file.exists():
            try:
                with open(profiles_file, 'r') as f:
                    profiles_data = json.load(f)
                
                for profile_data in profiles_data:
                    profile = AIAgentProfile(**profile_data)
                    self.agent_profiles[profile.agent_id] = profile
                
                self.logger.info(f"Loaded {len(self.agent_profiles)} agent profiles")
            except Exception as e:
                self.logger.error(f"Failed to load agent profiles: {e}")
    
    def _find_applicable_rules(self, decision_context: DecisionContext) -> List[GuidanceRule]:
        """Find guidance rules applicable to the decision"""
        applicable_rules = []
        
        for rule in self.guidance_rules:
            if rule.category == decision_context.category:
                # Simple keyword matching for now - could be enhanced with ML
                if any(keyword in decision_context.description.lower() 
                      for keyword in rule.condition.lower().split()):
                    applicable_rules.append(rule)
        
        return applicable_rules
    
    def _get_agent_specific_guidance(self, decision_context: DecisionContext) -> Dict[str, Any]:
        """Get guidance specific to the agent making the decision"""
        agent_profile = self.agent_profiles.get(decision_context.agent_id)
        
        if not agent_profile:
            return {"message": "No agent profile available"}
        
        # Check against agent's common mistakes
        potential_mistakes = []
        for mistake in agent_profile.common_mistakes:
            if mistake.lower() in decision_context.description.lower():
                potential_mistakes.append(mistake)
        
        # Check against agent's success patterns
        matching_patterns = []
        for pattern in agent_profile.success_patterns:
            if pattern.lower() in decision_context.description.lower():
                matching_patterns.append(pattern)
        
        return {
            "agent_experience": agent_profile.experience_level,
            "potential_mistakes": potential_mistakes,
            "matching_success_patterns": matching_patterns,
            "recommended_approach": agent_profile.preferred_approaches
        }
    
    def _analyze_decision_patterns(self, decision_context: DecisionContext) -> Dict[str, Any]:
        """Analyze historical patterns for similar decisions"""
        similar_decisions = [
            d for d in self.decision_history[-50:]  # Last 50 decisions
            if d.category == decision_context.category
            and any(file in decision_context.files_affected for file in d.files_affected)
        ]
        
        if not similar_decisions:
            return {"message": "No similar decisions found"}
        
        # Calculate success rate for similar decisions
        similar_guidance = [
            g for g in self.guidance_history
            if any(d.decision_id == g.decision_id for d in similar_decisions)
        ]
        
        success_count = len([g for g in similar_guidance if g.should_proceed])
        success_rate = success_count / len(similar_guidance) if similar_guidance else 0
        
        return {
            "similar_decisions_count": len(similar_decisions),
            "historical_success_rate": success_rate,
            "common_issues": self._extract_common_issues(similar_decisions),
            "recommended_validations": self._extract_recommended_validations(similar_guidance)
        }
    
    def _generate_guidance_response(self, decision_context: DecisionContext,
                                  applicable_rules: List[GuidanceRule],
                                  agent_guidance: Dict[str, Any],
                                  pattern_analysis: Dict[str, Any]) -> GuidanceResponse:
        """Generate comprehensive guidance response"""
        
        # Determine guidance level (highest from applicable rules)
        guidance_level = GuidanceLevel.INFO
        if applicable_rules:
            guidance_level = max(rule.guidance_level for rule in applicable_rules)
        
        # Check blocking conditions
        should_proceed = True
        blocking_reasons = []
        
        for rule in applicable_rules:
            for condition in rule.blocking_conditions:
                if self._check_blocking_condition(condition, decision_context):
                    should_proceed = False
                    blocking_reasons.append(condition)
        
        # Generate message
        messages = []
        if applicable_rules:
            messages.extend([rule.message for rule in applicable_rules])
        
        if agent_guidance.get("potential_mistakes"):
            messages.append(f"Warning: This matches your common mistake patterns: {', '.join(agent_guidance['potential_mistakes'])}")
        
        # Collect suggested actions
        suggested_actions = []
        for rule in applicable_rules:
            suggested_actions.extend(rule.suggested_actions)
        
        # Generate alternatives if blocked
        alternatives = []
        if not should_proceed:
            alternatives = self._generate_alternatives(decision_context, applicable_rules)
        
        # Calculate confidence
        confidence = 0.5  # Default
        if applicable_rules:
            confidence = sum(rule.confidence for rule in applicable_rules) / len(applicable_rules)
        
        return GuidanceResponse(
            decision_id=decision_context.decision_id,
            guidance_level=guidance_level,
            should_proceed=should_proceed,
            message="; ".join(messages) if messages else "No specific guidance available",
            suggested_actions=suggested_actions,
            alternative_approaches=alternatives,
            required_validations=self._generate_required_validations(decision_context, applicable_rules),
            estimated_risk=decision_context.risk_level,
            confidence=confidence
        )

    def _assess_change_risk(self, file_path: str, change_description: str) -> str:
        """Assess the risk level of a code change"""
        high_risk_files = [
            "app/main.py",
            "app/core/application.py",
            "app/database/manager.py",
            "app/ui/main_window.py"
        ]

        high_risk_keywords = [
            "event loop", "asyncio", "qapplication", "database", "schema",
            "migration", "delete", "drop", "remove"
        ]

        if any(file_path.endswith(risk_file) for risk_file in high_risk_files):
            return "high"

        if any(keyword in change_description.lower() for keyword in high_risk_keywords):
            return "high"

        if "test" in file_path.lower() or "debug" in file_path.lower():
            return "low"

        return "medium"

    def _classify_change_type(self, change_description: str) -> str:
        """Classify the type of change being made"""
        change_desc_lower = change_description.lower()

        if any(word in change_desc_lower for word in ["add", "create", "new"]):
            return "addition"
        elif any(word in change_desc_lower for word in ["remove", "delete", "drop"]):
            return "removal"
        elif any(word in change_desc_lower for word in ["fix", "bug", "error"]):
            return "bugfix"
        elif any(word in change_desc_lower for word in ["refactor", "restructure", "reorganize"]):
            return "refactoring"
        elif any(word in change_desc_lower for word in ["optimize", "performance", "speed"]):
            return "optimization"
        else:
            return "modification"

    def _calculate_success_rate(self, agent_id: str) -> float:
        """Calculate success rate for an agent"""
        agent_decisions = [d for d in self.decision_history if d.agent_id == agent_id]
        if not agent_decisions:
            return 0.0

        agent_guidance = [
            g for g in self.guidance_history
            if any(d.decision_id == g.decision_id and d.agent_id == agent_id for d in agent_decisions)
        ]

        successful_decisions = len([g for g in agent_guidance if g.should_proceed])
        return successful_decisions / len(agent_guidance) if agent_guidance else 0.0

    def _identify_common_issues(self, agent_id: str) -> List[str]:
        """Identify common issues for an agent"""
        agent_decisions = [d for d in self.decision_history[-20:] if d.agent_id == agent_id]

        # Look for patterns in blocked decisions
        blocked_guidance = [
            g for g in self.guidance_history
            if not g.should_proceed and any(d.decision_id == g.decision_id and d.agent_id == agent_id for d in agent_decisions)
        ]

        # Extract common themes from blocked decisions
        common_issues = []
        issue_patterns = {
            "testing": ["test", "validation", "verify"],
            "documentation": ["document", "comment", "explain"],
            "error_handling": ["error", "exception", "failure"],
            "performance": ["performance", "optimization", "speed"],
            "security": ["security", "vulnerability", "safe"]
        }

        for issue_type, keywords in issue_patterns.items():
            if any(any(keyword in g.message.lower() for keyword in keywords) for g in blocked_guidance):
                common_issues.append(issue_type)

        return common_issues

    def _suggest_improvements(self, agent_id: str) -> List[str]:
        """Suggest improvements for an agent"""
        common_issues = self._identify_common_issues(agent_id)

        improvement_suggestions = {
            "testing": "Focus on comprehensive testing before making changes",
            "documentation": "Improve code documentation and change explanations",
            "error_handling": "Add better error handling and recovery mechanisms",
            "performance": "Consider performance implications of changes",
            "security": "Review security implications of modifications"
        }

        return [improvement_suggestions.get(issue, f"Address {issue} concerns") for issue in common_issues]

    def _calculate_average_risk(self, agent_id: str) -> str:
        """Calculate average risk level for an agent's decisions"""
        agent_decisions = [d for d in self.decision_history if d.agent_id == agent_id]
        if not agent_decisions:
            return "unknown"

        risk_scores = {"low": 1, "medium": 2, "high": 3}
        total_score = sum(risk_scores.get(d.risk_level, 2) for d in agent_decisions)
        average_score = total_score / len(agent_decisions)

        if average_score <= 1.5:
            return "low"
        elif average_score <= 2.5:
            return "medium"
        else:
            return "high"

    def _calculate_guidance_adherence(self, agent_id: str) -> float:
        """Calculate how well an agent follows guidance"""
        # This would require tracking whether agents actually follow suggestions
        # For now, return a placeholder
        return 0.8  # 80% adherence rate

    def _identify_strengths(self, agent_id: str) -> List[str]:
        """Identify an agent's strengths"""
        agent_profile = self.agent_profiles.get(agent_id)
        if agent_profile:
            return agent_profile.areas_of_expertise

        # Analyze from decision history
        agent_decisions = [d for d in self.decision_history if d.agent_id == agent_id]
        successful_categories: Dict[str, int] = {}

        for decision in agent_decisions:
            guidance = next((g for g in self.guidance_history if g.decision_id == decision.decision_id), None)
            if guidance and guidance.should_proceed:
                category = decision.category.value
                successful_categories[category] = successful_categories.get(category, 0) + 1

        # Return categories where agent has high success rate
        return [category for category, count in successful_categories.items() if count >= 3]

    def _extract_common_issues(self, decisions: List[DecisionContext]) -> List[str]:
        """Extract common issues from similar decisions"""
        issues = []
        for decision in decisions:
            if "error" in decision.description.lower():
                issues.append("error_prone_area")
            if "performance" in decision.description.lower():
                issues.append("performance_concern")
            if "security" in decision.description.lower():
                issues.append("security_risk")

        return list(set(issues))

    def _extract_recommended_validations(self, guidance_responses: List[GuidanceResponse]) -> List[str]:
        """Extract recommended validations from guidance history"""
        validations = []
        for guidance in guidance_responses:
            validations.extend(guidance.required_validations)

        return list(set(validations))

    def _check_blocking_condition(self, condition: str, decision_context: DecisionContext) -> bool:
        """Check if a blocking condition is met"""
        # Simple keyword-based checking - could be enhanced
        condition_lower = condition.lower()

        if "no tests" in condition_lower:
            # Check if there are test files for the affected files
            for file_path in decision_context.files_affected:
                test_file = file_path.replace(".py", "_test.py").replace("app/", "tests/")
                if not Path(test_file).exists():
                    return True

        if "previous.*failures" in condition_lower:
            # Check if similar decisions have failed before
            similar_decisions = [
                d for d in self.decision_history
                if d.category == decision_context.category
                and any(file in decision_context.files_affected for file in d.files_affected)
            ]

            failed_guidance = [
                g for g in self.guidance_history
                if not g.should_proceed and any(d.decision_id == g.decision_id for d in similar_decisions)
            ]

            return len(failed_guidance) > 0

        return False

    def _generate_alternatives(self, decision_context: DecisionContext,
                             applicable_rules: List[GuidanceRule]) -> List[str]:
        """Generate alternative approaches when an action is blocked"""
        alternatives = []

        if decision_context.category == DecisionCategory.CODE_CHANGE:
            alternatives.extend([
                "Create a feature branch for experimental changes",
                "Implement changes incrementally with testing at each step",
                "Use feature flags to control new functionality",
                "Create a proof-of-concept in a separate module first"
            ])

        if decision_context.category == DecisionCategory.TESTING:
            alternatives.extend([
                "Start with unit tests for individual components",
                "Create integration tests for critical paths",
                "Use mocking to isolate components under test"
            ])

        return alternatives

    def _generate_required_validations(self, decision_context: DecisionContext,
                                     applicable_rules: List[GuidanceRule]) -> List[str]:
        """Generate required validations for a decision"""
        validations = []

        # Add validations from applicable rules
        for rule in applicable_rules:
            validations.extend(rule.suggested_actions)

        # Add category-specific validations
        if decision_context.category == DecisionCategory.CODE_CHANGE:
            validations.extend([
                "Run existing tests to ensure no regressions",
                "Verify code follows project style guidelines",
                "Check for potential security implications"
            ])

        if decision_context.category == DecisionCategory.DEPENDENCY:
            validations.extend([
                "Check dependency license compatibility",
                "Verify no known security vulnerabilities",
                "Ensure dependency is actively maintained"
            ])

        return list(set(validations))  # Remove duplicates

    def save_session_data(self, filepath: Path) -> None:
        """Save guidance session data for analysis"""
        session_data = {
            "timestamp": datetime.now().isoformat(),
            "agent_profiles": {aid: asdict(profile) for aid, profile in self.agent_profiles.items()},
            "decision_history": [asdict(decision) for decision in self.decision_history],
            "guidance_history": [asdict(guidance) for guidance in self.guidance_history],
            "guidance_rules": [asdict(rule) for rule in self.guidance_rules]
        }

        with open(filepath, 'w') as f:
            json.dump(session_data, f, indent=2)

        self.logger.info(f"Guidance session data saved to {filepath}")


# Global guidance framework instance
ai_guidance_framework = AIGuidanceFramework()


def request_ai_guidance(decision_context: DecisionContext) -> GuidanceResponse:
    """Convenience function for requesting guidance"""
    return ai_guidance_framework.request_guidance(decision_context)


def evaluate_code_change(file_path: str, change_description: str, agent_id: str) -> GuidanceResponse:
    """Convenience function for evaluating code changes"""
    return ai_guidance_framework.evaluate_code_change(file_path, change_description, agent_id)
