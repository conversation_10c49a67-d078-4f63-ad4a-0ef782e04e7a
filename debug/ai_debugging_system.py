"""
AI Debugging System for Kate LLM Client.

Comprehensive debugging and logging tools specifically designed for AI development.
Provides intelligent error handling, pattern recognition, and recovery suggestions.
"""

import json
import re
import traceback
from dataclasses import asdict, dataclass
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

from loguru import logger


@dataclass
class ErrorPattern:
    """Pattern for matching common errors"""
    id: str
    pattern: str
    description: str
    recovery_strategy: str
    severity: str  # "critical", "high", "medium", "low"
    category: str  # "ui", "database", "network", "dependency", "logic"


@dataclass
class RecoveryAction:
    """Suggested recovery action for an error"""
    action_type: str  # "code_change", "dependency_install", "config_update", "restart"
    description: str
    commands: List[str]
    files_to_modify: List[str]
    priority: int  # 1-10, 1 being highest priority
    estimated_time_minutes: int


@dataclass
class DebuggingContext:
    """Context information for debugging"""
    timestamp: str
    function_name: str
    file_path: str
    line_number: int
    local_variables: Dict[str, Any]
    stack_trace: str
    error_message: str
    system_state: Dict[str, Any]


class AIErrorHandler:
    """Comprehensive error handling designed for AI debugging"""
    
    def __init__(self):
        self.error_patterns = self._load_error_patterns()
        self.recovery_strategies = self._load_recovery_strategies()
        self.error_history: List[DebuggingContext] = []
        self.logger = logger.bind(component="AIErrorHandler")
    
    def handle_error(self, error: Exception, context: Dict[str, Any] = None) -> RecoveryAction:
        """
        Intelligent error handling with recovery suggestions.
        
        Args:
            error: The exception that occurred
            context: Additional context information
            
        Returns:
            RecoveryAction with specific steps to resolve the issue
        """
        # Create debugging context
        debug_context = self._create_debugging_context(error, context or {})
        self.error_history.append(debug_context)
        
        # Pattern match against known issues
        pattern = self._match_error_pattern(error, debug_context)
        
        if pattern:
            recovery = self.recovery_strategies.get(pattern.id)
            if recovery:
                self.logger.info(f"Found recovery strategy for {pattern.id}: {recovery.description}")
                return recovery
        
        # Fallback to generic recovery
        return self._create_generic_recovery(error, debug_context)
    
    def analyze_error_trends(self) -> Dict[str, Any]:
        """Analyze patterns in error history"""
        if not self.error_history:
            return {"message": "No error history available"}
        
        # Count errors by category
        error_counts = {}
        recent_errors = []
        
        for ctx in self.error_history[-10:]:  # Last 10 errors
            error_type = type(ctx.error_message).__name__ if hasattr(ctx, 'error_message') else "Unknown"
            error_counts[error_type] = error_counts.get(error_type, 0) + 1
            recent_errors.append({
                "timestamp": ctx.timestamp,
                "error": ctx.error_message,
                "function": ctx.function_name,
                "file": ctx.file_path
            })
        
        return {
            "total_errors": len(self.error_history),
            "error_counts": error_counts,
            "recent_errors": recent_errors,
            "most_common": max(error_counts.items(), key=lambda x: x[1]) if error_counts else None
        }
    
    def suggest_debugging_approach(self, error: Exception) -> Dict[str, Any]:
        """Suggest debugging approach for specific errors"""
        error_type = type(error).__name__
        error_msg = str(error)
        
        suggestions = {
            "immediate_actions": [],
            "investigation_steps": [],
            "tools_to_use": [],
            "files_to_check": []
        }
        
        # Qt/UI related errors
        if "QApplication" in error_msg or "Qt" in error_msg:
            suggestions["immediate_actions"].extend([
                "Check if QApplication is properly initialized",
                "Verify Qt event loop is running",
                "Check for widget hierarchy issues"
            ])
            suggestions["tools_to_use"].extend([
                "emergency_ui_test.py",
                "ai_testing_framework.py"
            ])
            suggestions["files_to_check"].extend([
                "app/main.py",
                "app/ui/main_window.py"
            ])
        
        # Database errors
        elif "database" in error_msg.lower() or "sql" in error_msg.lower():
            suggestions["immediate_actions"].extend([
                "Check database connection",
                "Verify database file exists",
                "Check database schema"
            ])
            suggestions["files_to_check"].extend([
                "app/database/manager.py",
                "app/core/application.py"
            ])
        
        # Import/dependency errors
        elif "ImportError" in error_type or "ModuleNotFoundError" in error_type:
            suggestions["immediate_actions"].extend([
                "Check if dependency is installed",
                "Verify virtual environment is activated",
                "Check requirements.txt or pyproject.toml"
            ])
            suggestions["tools_to_use"].append("pip list")
        
        # Event loop errors
        elif "event loop" in error_msg.lower() or "asyncio" in error_msg.lower():
            suggestions["immediate_actions"].extend([
                "Check asyncio event loop setup",
                "Verify qasync integration",
                "Check for blocking operations in main thread"
            ])
            suggestions["files_to_check"].extend([
                "app/main.py",
                "app/core/application.py"
            ])
        
        return suggestions
    
    def _create_debugging_context(self, error: Exception, context: Dict[str, Any]) -> DebuggingContext:
        """Create comprehensive debugging context"""
        import inspect
        
        # Get stack frame information
        frame = inspect.currentframe()
        try:
            # Go up the stack to find the actual error location
            while frame and frame.f_code.co_filename.endswith('ai_debugging_system.py'):
                frame = frame.f_back
            
            if frame:
                filename = frame.f_code.co_filename
                function_name = frame.f_code.co_name
                line_number = frame.f_lineno
                local_vars = {k: str(v)[:100] for k, v in frame.f_locals.items()}  # Truncate long values
            else:
                filename = "unknown"
                function_name = "unknown"
                line_number = 0
                local_vars = {}
        finally:
            del frame
        
        return DebuggingContext(
            timestamp=datetime.now().isoformat(),
            function_name=function_name,
            file_path=filename,
            line_number=line_number,
            local_variables=local_vars,
            stack_trace=traceback.format_exc(),
            error_message=str(error),
            system_state=context
        )
    
    def _match_error_pattern(self, error: Exception, context: DebuggingContext) -> Optional[ErrorPattern]:
        """Match error against known patterns"""
        error_text = f"{type(error).__name__}: {str(error)}"
        
        for pattern in self.error_patterns:
            if re.search(pattern.pattern, error_text, re.IGNORECASE):
                return pattern
        
        return None
    
    def _create_generic_recovery(self, error: Exception, context: DebuggingContext) -> RecoveryAction:
        """Create generic recovery action"""
        return RecoveryAction(
            action_type="investigation",
            description=f"Generic recovery for {type(error).__name__}",
            commands=[
                "Check logs for more details",
                "Verify system state",
                "Review recent changes"
            ],
            files_to_modify=[],
            priority=5,
            estimated_time_minutes=15
        )
    
    def _load_error_patterns(self) -> List[ErrorPattern]:
        """Load common error patterns for Kate development"""
        return [
            ErrorPattern(
                id="qt_application_singleton",
                pattern=r"QApplication.*singleton",
                description="QApplication singleton error",
                recovery_strategy="restart_qt_app",
                severity="high",
                category="ui"
            ),
            ErrorPattern(
                id="event_loop_not_running",
                pattern=r"no running event loop",
                description="Asyncio event loop not running",
                recovery_strategy="fix_event_loop",
                severity="critical",
                category="ui"
            ),
            ErrorPattern(
                id="module_not_found",
                pattern=r"ModuleNotFoundError.*'(\w+)'",
                description="Missing Python module",
                recovery_strategy="install_dependency",
                severity="high",
                category="dependency"
            ),
            ErrorPattern(
                id="database_connection",
                pattern=r"database.*connection|sqlite.*error",
                description="Database connection issue",
                recovery_strategy="fix_database",
                severity="medium",
                category="database"
            ),
            ErrorPattern(
                id="widget_not_found",
                pattern=r"widget.*not found|findChildren.*empty",
                description="UI widget not found",
                recovery_strategy="check_widget_hierarchy",
                severity="medium",
                category="ui"
            )
        ]
    
    def _load_recovery_strategies(self) -> Dict[str, RecoveryAction]:
        """Load recovery strategies for common issues"""
        return {
            "restart_qt_app": RecoveryAction(
                action_type="code_change",
                description="Fix QApplication singleton issue",
                commands=[
                    "Check for existing QApplication instance",
                    "Use QApplication.instance() instead of creating new"
                ],
                files_to_modify=["app/main.py"],
                priority=1,
                estimated_time_minutes=5
            ),
            "fix_event_loop": RecoveryAction(
                action_type="code_change",
                description="Fix asyncio event loop setup",
                commands=[
                    "Ensure event loop is created before tasks",
                    "Use qasync for Qt/asyncio integration",
                    "Check main_async() waits for shutdown"
                ],
                files_to_modify=["app/main.py"],
                priority=1,
                estimated_time_minutes=10
            ),
            "install_dependency": RecoveryAction(
                action_type="dependency_install",
                description="Install missing Python dependency",
                commands=[
                    "source kate_env/bin/activate",
                    "pip install <missing_module>"
                ],
                files_to_modify=[],
                priority=2,
                estimated_time_minutes=3
            ),
            "fix_database": RecoveryAction(
                action_type="config_update",
                description="Fix database connection issues",
                commands=[
                    "Check database file permissions",
                    "Verify database URL in settings",
                    "Run database migrations"
                ],
                files_to_modify=["app/core/config.py"],
                priority=3,
                estimated_time_minutes=8
            ),
            "check_widget_hierarchy": RecoveryAction(
                action_type="investigation",
                description="Investigate widget hierarchy issues",
                commands=[
                    "Run widget hierarchy diagnostic",
                    "Check widget object names",
                    "Verify widget creation order"
                ],
                files_to_modify=["app/ui/main_window.py"],
                priority=4,
                estimated_time_minutes=12
            )
        }
    
    def save_debugging_session(self, filepath: Path) -> None:
        """Save debugging session for later analysis"""
        session_data = {
            "timestamp": datetime.now().isoformat(),
            "error_history": [asdict(ctx) for ctx in self.error_history],
            "error_analysis": self.analyze_error_trends()
        }
        
        with open(filepath, 'w') as f:
            json.dump(session_data, f, indent=2)
        
        self.logger.info(f"Debugging session saved to {filepath}")


# Global error handler instance
ai_error_handler = AIErrorHandler()


def handle_ai_error(error: Exception, context: Dict[str, Any] = None) -> RecoveryAction:
    """Convenience function for handling errors"""
    return ai_error_handler.handle_error(error, context)


def get_debugging_suggestions(error: Exception) -> Dict[str, Any]:
    """Get debugging suggestions for an error"""
    return ai_error_handler.suggest_debugging_approach(error)
