[tool.poetry]
name = "kate"
version = "1.0.0"
description = "Kate LLM Client - A modern desktop client for multiple LLM providers"
authors = ["Kate Team <<EMAIL>>"]
license = "MIT"
readme = "README.md"
homepage = "https://github.com/kate-llm/kate"
repository = "https://github.com/kate-llm/kate"
documentation = "https://docs.kate-llm.com"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Application Frameworks",
]
packages = [{include = "app"}]

[tool.poetry.dependencies]
python = ">=3.9,<3.13"
PySide6 = "^6.6.0"
pydantic = "^2.5.0"
pydantic-settings = "^2.1.0"
sqlalchemy = "^2.0.23"
aiosqlite = "^0.19.0"
alembic = "^1.13.0"
loguru = "^0.7.2"
click = "^8.1.7"
httpx = "^0.25.2"
openai = "^1.6.0"
anthropic = "^0.7.8"
requests = "^2.31.0"
aiofiles = "^23.2.1"
pillow = "^10.1.0"
pypdf2 = "^3.0.1"
python-multipart = "^0.0.6"
websockets = "^12.0"
uvloop = {version = "^0.19.0", markers = "sys_platform != 'win32'"}
watchfiles = "^0.21.0"
cryptography = ">=3.4.8"
keyring = ">=23.0.0"
psutil = ">=5.8.0"
schedule = "^1.2.0"
colorama = "^0.4.6"
rich = "^13.7.0"
typer = "^0.9.0"
fastapi = "^0.104.1"
uvicorn = "^0.24.0"
jinja2 = "^3.1.2"
markdown = "^3.5.1"
beautifulsoup4 = "^4.12.2"
lxml = "^4.9.3"
feedparser = "^6.0.10"
python-dateutil = "^2.8.2"
python-magic = "^0.4.27"
chardet = "^5.2.0"
ftfy = "^6.1.1"
tiktoken = "^0.5.2"
transformers = "^4.36.0"
torch = "^2.1.1"
tokenizers = "^0.15.0"
numpy = "^1.24.4"
scipy = "^1.11.4"
scikit-learn = "^1.3.2"
pandas = "^2.1.4"
matplotlib = "^3.8.2"
seaborn = "^0.13.0"
plotly = "^5.17.0"
networkx = "^3.2.1"
nltk = "^3.8.1"
spacy = "^3.7.2"
textblob = "^0.17.1"
langdetect = "^1.0.9"
# googletrans = "^3.1.0a0"  # Removed due to httpx version conflict
python-dotenv = "^1.0.0"
pyyaml = "^6.0.1"
toml = "^0.10.2"
configparser = "^6.0.0"
appdirs = "^1.4.4"
platformdirs = "^4.1.0"
packaging = "^23.2"
semantic-version = "^2.10.0"
distro = "^1.8.0"
py-cpuinfo = "^9.0.0"
GPUtil = "^1.4.0"
pynvml = "^11.5.0"
cuda-python = {version = "^12.3.0", optional = true}
cupy-cuda12x = {version = "^12.3.0", optional = true}
google-generativeai = {version = "^0.3.2", optional = true}
groq = {version = "^0.4.1", optional = true}
cohere = {version = "^4.37", optional = true}
mistralai = {version = "^0.0.12", optional = true}
ollama = {version = "^0.1.7", optional = true}
speechrecognition = {version = "^3.10.0", optional = true}
pyttsx3 = {version = "^2.90", optional = true}
pyaudio = {version = "^0.2.11", optional = true}
sounddevice = {version = "^0.4.6", optional = true}
soundfile = {version = "^0.12.1", optional = true}
librosa = {version = "^0.10.1", optional = true}
whisper = {version = "^1.1.10", optional = true}
edge-tts = {version = "^6.1.9", optional = true}
gtts = {version = "^2.5.1", optional = true}
pygame = {version = "^2.5.2", optional = true}
pytesseract = {version = "^0.3.10", optional = true}
easyocr = {version = "^1.7.0", optional = true}
pdf2image = {version = "^1.16.3", optional = true}
opencv-python = {version = "^4.8.1.78", optional = true}
openpyxl = {version = "^3.1.2", optional = true}
python-docx = {version = "^1.1.0", optional = true}
html2text = {version = "^2020.1.16", optional = true}
pdfplumber = {version = "^0.10.3", optional = true}

[tool.poetry.extras]
cuda = ["cuda-python", "cupy-cuda12x"]
google = ["google-generativeai"]
groq = ["groq"]
cohere = ["cohere"]
mistral = ["mistralai"]
ollama = ["ollama"]
speech = ["speechrecognition", "pyttsx3", "pyaudio", "sounddevice", "soundfile", "librosa", "whisper", "edge-tts", "gtts", "pygame"]
ocr = ["pytesseract", "easyocr", "pdf2image", "opencv-python"]
documents = ["openpyxl", "python-docx", "html2text", "pdfplumber"]
all = ["cuda-python", "cupy-cuda12x", "google-generativeai", "groq", "cohere", "mistralai", "ollama", "speechrecognition", "pyttsx3", "pyaudio", "sounddevice", "soundfile", "librosa", "whisper", "edge-tts", "gtts", "pygame", "pytesseract", "easyocr", "pdf2image", "opencv-python", "openpyxl", "python-docx", "html2text", "pdfplumber"]

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.3"
pytest-asyncio = "^0.21.1"
pytest-cov = "^4.1.0"
pytest-mock = "^3.12.0"
pytest-qt = "^4.2.0"
black = "^23.11.0"
ruff = "^0.1.6"
mypy = "^1.7.1"
pre-commit = "^3.6.0"
coverage = "^7.3.2"
sphinx = "^7.2.6"
sphinx-rtd-theme = "^1.3.0"
myst-parser = "^2.0.0"
mkdocs = "^1.5.3"
mkdocs-material = "^9.4.8"
mkdocs-mermaid2-plugin = "^1.1.1"

[tool.poetry.group.build.dependencies]
pyinstaller = "^6.2.0"
cx-freeze = "^6.15.10"
nuitka = "^1.8.4"
briefcase = "^0.3.17"

[tool.poetry.scripts]
kate = "app.main:main"
kate-dev = "app.main:dev_main"
kate-test = "app.main:test_main"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 100
target-version = ['py39', 'py310', 'py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.ruff]
line-length = 100
target-version = "py39"
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]

[tool.mypy]
python_version = "3.9"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_decorators = true
disallow_untyped_defs = true
ignore_missing_imports = true
no_implicit_optional = true
no_implicit_reexport = true
show_error_codes = true
strict_equality = true
warn_redundant_casts = true
warn_return_any = true
warn_unreachable = true
warn_unused_configs = true
warn_unused_ignores = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--cov=app",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "ui: marks tests as UI tests",
    "api: marks tests as API tests",
]
asyncio_mode = "auto"

[tool.coverage.run]
source = ["app"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]