#!/usr/bin/env python3
"""
Test Kate UI Functionality
Quick test to verify <PERSON>'s UI is actually responsive to user input
"""

import asyncio
import os
import signal
import sys
from pathlib import Path

# Add app directory to path
sys.path.insert(0, str(Path(__file__).parent / "app"))

try:
    from PySide6.QtCore import QTimer
    from PySide6.QtTest import QTest
    from PySide6.QtWidgets import QApplication
    from qasync import QEvent<PERSON>oop

    from app.core.application import KateApplication
    from app.main import setup_platform, setup_qt_application
    
    print("✅ All imports successful")
    
except ImportError as e:
    print(f"❌ Import failed: {e}")
    sys.exit(1)


async def test_kate_ui_functionality():
    """Test Kate UI functionality"""
    
    print("\n🧪 TESTING KATE UI FUNCTIONALITY")
    print("=" * 50)
    
    kate_app = None
    try:
        # Setup platform
        setup_platform()
        print("✅ Platform setup complete")
        
        # Initialize Kate application
        kate_app = KateApplication()
        await kate_app.startup()
        print("✅ Kate application started")
        
        # Get main window
        main_window = kate_app.get_main_window()
        if not main_window:
            print("❌ Main window not found")
            return False
        
        print(f"✅ Main window found: {main_window}")
        print(f"   - Visible: {main_window.isVisible()}")
        print(f"   - Size: {main_window.size()}")
        
        # Test basic UI interactions
        print("\n🔍 Testing UI interactions...")
        
        # Find chat input field
        chat_inputs = main_window.findChildren(object, "message_input")
        if chat_inputs:
            chat_input = chat_inputs[0]
            print(f"✅ Found chat input: {chat_input}")
            
            # Test typing in chat input
            if hasattr(chat_input, 'setText') and hasattr(chat_input, 'text'):
                test_message = "Hello, this is a test message!"
                chat_input.setText(test_message)
                
                # Process events to ensure the text is set
                QApplication.instance().processEvents()
                
                actual_text = chat_input.text()
                if actual_text == test_message:
                    print("✅ Chat input accepts text input")
                else:
                    print(f"❌ Chat input text mismatch: expected '{test_message}', got '{actual_text}'")
            else:
                print("❌ Chat input doesn't support text operations")
        else:
            print("❌ Chat input field not found")
        
        # Find send button
        send_buttons = main_window.findChildren(object, "send_button")
        if send_buttons:
            send_button = send_buttons[0]
            print(f"✅ Found send button: {send_button}")
            
            if hasattr(send_button, 'isEnabled'):
                if send_button.isEnabled():
                    print("✅ Send button is enabled")
                else:
                    print("❌ Send button is disabled")
            
            # Test clicking send button (simulate)
            if hasattr(send_button, 'click'):
                print("✅ Send button supports click events")
            else:
                print("❌ Send button doesn't support click events")
        else:
            print("❌ Send button not found")
        
        # Test event processing
        print("\n⚡ Testing event processing...")
        
        # Create a timer to test event loop
        timer_fired = False
        def timer_callback():
            nonlocal timer_fired
            timer_fired = True
            print("✅ Timer fired - event loop is processing events!")
        
        timer = QTimer()
        timer.timeout.connect(timer_callback)
        timer.setSingleShot(True)
        timer.start(500)  # 500ms
        
        # Wait for timer with timeout
        for i in range(20):  # Wait up to 2 seconds
            QApplication.instance().processEvents()
            await asyncio.sleep(0.1)
            if timer_fired:
                break
        
        if timer_fired:
            print("✅ Event loop is working correctly")
        else:
            print("❌ Event loop not processing events")
        
        # Test window interactions
        print("\n🖱️ Testing window interactions...")
        
        # Test window focus
        main_window.activateWindow()
        main_window.raise_()
        QApplication.instance().processEvents()
        
        if main_window.isActiveWindow():
            print("✅ Window can be activated")
        else:
            print("⚠️ Window activation unclear (may be normal in headless environment)")
        
        print("\n🎉 UI FUNCTIONALITY TEST COMPLETE")
        
        # Keep running for a few seconds to allow manual testing
        print("⏳ Keeping Kate running for 5 seconds for manual testing...")
        await asyncio.sleep(5)
        
        return True
        
    except Exception as e:
        print(f"❌ Error during UI testing: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        if kate_app:
            print("🛑 Shutting down Kate...")
            await kate_app.shutdown()


async def main():
    """Main test function"""

    # Run the test
    success = await test_kate_ui_functionality()

    if success:
        print("\n✅ KATE UI IS FUNCTIONAL!")
        print("🎯 Your job is safe - Kate works!")
    else:
        print("\n❌ KATE UI HAS ISSUES")
        print("🚨 More work needed")

    return success


if __name__ == "__main__":
    try:
        # Create Qt application
        app = QApplication.instance()
        if not app:
            app = QApplication(sys.argv)

        # Setup signal handlers
        def signal_handler(sig, frame):
            print(f"\n⚠️ Received signal {sig}, shutting down...")
            app.quit()

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        # Use qasync for proper Qt/asyncio integration
        loop = QEventLoop(app)
        asyncio.set_event_loop(loop)

        # Run the test
        success = loop.run_until_complete(main())

        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
