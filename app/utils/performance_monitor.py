"""
Performance monitoring utilities for Kate LLM Client.

Provides memory usage tracking, performance metrics, and optimization tools.
"""

import asyncio
import gc
import os
import time
from dataclasses import dataclass
from typing import Dict, List, Optional

import psutil
from loguru import logger


@dataclass
class PerformanceMetrics:
    """Performance metrics snapshot."""
    timestamp: float
    memory_usage_mb: float
    cpu_percent: float
    active_threads: int
    open_files: int
    network_connections: int
    gc_collections: Dict[int, int]


class PerformanceMonitor:
    """
    Monitor application performance and resource usage.
    
    Tracks memory usage, CPU utilization, and provides optimization
    recommendations for Kate LLM Client.
    """
    
    def __init__(self):
        self.logger = logger.bind(component="PerformanceMonitor")
        self.process = psutil.Process(os.getpid())
        self.metrics_history: List[PerformanceMetrics] = []
        self.monitoring_active = False
        self._monitor_task: Optional[asyncio.Task] = None
        
        # Performance thresholds
        self.memory_warning_mb = 500  # Warn if over 500MB
        self.memory_critical_mb = 1000  # Critical if over 1GB
        self.cpu_warning_percent = 80  # Warn if over 80% CPU
        
    async def start_monitoring(self, interval_seconds: float = 30.0) -> None:
        """Start continuous performance monitoring."""
        if self.monitoring_active:
            self.logger.warning("Performance monitoring already active")
            return
        
        self.monitoring_active = True
        self._monitor_task = asyncio.create_task(self._monitor_loop(interval_seconds))
        self.logger.info(f"Started performance monitoring (interval: {interval_seconds}s)")
    
    async def stop_monitoring(self) -> None:
        """Stop performance monitoring."""
        if not self.monitoring_active:
            return
        
        self.monitoring_active = False
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("Stopped performance monitoring")
    
    async def _monitor_loop(self, interval_seconds: float) -> None:
        """Main monitoring loop."""
        while self.monitoring_active:
            try:
                metrics = self.capture_metrics()
                self.metrics_history.append(metrics)
                
                # Keep only last 100 metrics (about 50 minutes at 30s intervals)
                if len(self.metrics_history) > 100:
                    self.metrics_history = self.metrics_history[-100:]
                
                # Check for performance issues
                self._check_performance_thresholds(metrics)
                
                await asyncio.sleep(interval_seconds)
                
            except Exception as e:
                self.logger.error(f"Error in performance monitoring: {e}")
                await asyncio.sleep(interval_seconds)
    
    def capture_metrics(self) -> PerformanceMetrics:
        """Capture current performance metrics."""
        try:
            # Memory info
            memory_info = self.process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            
            # CPU usage
            cpu_percent = self.process.cpu_percent()
            
            # Thread count
            thread_count = self.process.num_threads()
            
            # Open files
            try:
                open_files = len(self.process.open_files())
            except (psutil.AccessDenied, psutil.NoSuchProcess):
                open_files = 0
            
            # Network connections
            try:
                connections = len(self.process.connections())
            except (psutil.AccessDenied, psutil.NoSuchProcess):
                connections = 0
            
            # Garbage collection stats
            gc_stats = {}
            for generation in range(3):
                gc_stats[generation] = gc.get_count()[generation]
            
            return PerformanceMetrics(
                timestamp=time.time(),
                memory_usage_mb=memory_mb,
                cpu_percent=cpu_percent,
                active_threads=thread_count,
                open_files=open_files,
                network_connections=connections,
                gc_collections=gc_stats
            )
            
        except Exception as e:
            self.logger.error(f"Failed to capture metrics: {e}")
            return PerformanceMetrics(
                timestamp=time.time(),
                memory_usage_mb=0,
                cpu_percent=0,
                active_threads=0,
                open_files=0,
                network_connections=0,
                gc_collections={}
            )
    
    def _check_performance_thresholds(self, metrics: PerformanceMetrics) -> None:
        """Check if performance metrics exceed thresholds."""
        
        # Memory usage checks
        if metrics.memory_usage_mb > self.memory_critical_mb:
            self.logger.critical(f"Critical memory usage: {metrics.memory_usage_mb:.1f}MB")
            self._suggest_memory_optimization()
        elif metrics.memory_usage_mb > self.memory_warning_mb:
            self.logger.warning(f"High memory usage: {metrics.memory_usage_mb:.1f}MB")
        
        # CPU usage checks
        if metrics.cpu_percent > self.cpu_warning_percent:
            self.logger.warning(f"High CPU usage: {metrics.cpu_percent:.1f}%")
        
        # Thread count checks
        if metrics.active_threads > 50:
            self.logger.warning(f"High thread count: {metrics.active_threads}")
        
        # Open files checks
        if metrics.open_files > 100:
            self.logger.warning(f"Many open files: {metrics.open_files}")
    
    def _suggest_memory_optimization(self) -> None:
        """Suggest memory optimization actions."""
        self.logger.info("Memory optimization suggestions:")
        self.logger.info("  - Run garbage collection")
        self.logger.info("  - Clear conversation history cache")
        self.logger.info("  - Close unused database connections")
        self.logger.info("  - Reduce UI widget cache size")
        
        # Trigger garbage collection
        collected = gc.collect()
        self.logger.info(f"Garbage collection freed {collected} objects")
    
    def get_performance_summary(self) -> Dict[str, any]:
        """Get performance summary statistics."""
        if not self.metrics_history:
            return {"status": "no_data"}
        
        recent_metrics = self.metrics_history[-10:]  # Last 10 measurements
        
        avg_memory = sum(m.memory_usage_mb for m in recent_metrics) / len(recent_metrics)
        max_memory = max(m.memory_usage_mb for m in recent_metrics)
        avg_cpu = sum(m.cpu_percent for m in recent_metrics) / len(recent_metrics)
        max_cpu = max(m.cpu_percent for m in recent_metrics)
        
        return {
            "status": "active" if self.monitoring_active else "inactive",
            "measurements": len(self.metrics_history),
            "memory": {
                "current_mb": recent_metrics[-1].memory_usage_mb,
                "average_mb": avg_memory,
                "peak_mb": max_memory,
                "warning_threshold_mb": self.memory_warning_mb,
                "critical_threshold_mb": self.memory_critical_mb
            },
            "cpu": {
                "current_percent": recent_metrics[-1].cpu_percent,
                "average_percent": avg_cpu,
                "peak_percent": max_cpu,
                "warning_threshold_percent": self.cpu_warning_percent
            },
            "threads": recent_metrics[-1].active_threads,
            "open_files": recent_metrics[-1].open_files,
            "network_connections": recent_metrics[-1].network_connections
        }
    
    def optimize_memory(self) -> Dict[str, any]:
        """Perform memory optimization actions."""
        self.logger.info("Starting memory optimization...")
        
        initial_metrics = self.capture_metrics()
        initial_memory = initial_metrics.memory_usage_mb
        
        # Force garbage collection
        collected_objects = gc.collect()
        
        # Get updated metrics
        final_metrics = self.capture_metrics()
        final_memory = final_metrics.memory_usage_mb
        
        memory_freed = initial_memory - final_memory
        
        optimization_result = {
            "initial_memory_mb": initial_memory,
            "final_memory_mb": final_memory,
            "memory_freed_mb": memory_freed,
            "objects_collected": collected_objects,
            "optimization_effective": memory_freed > 1.0  # At least 1MB freed
        }
        
        self.logger.info(f"Memory optimization complete: freed {memory_freed:.1f}MB")
        return optimization_result
    
    def get_memory_usage_trend(self) -> List[float]:
        """Get memory usage trend over time."""
        return [m.memory_usage_mb for m in self.metrics_history[-20:]]  # Last 20 measurements
    
    def detect_memory_leaks(self) -> bool:
        """Detect potential memory leaks based on usage trends."""
        if len(self.metrics_history) < 10:
            return False
        
        # Check if memory usage is consistently increasing
        recent_memory = [m.memory_usage_mb for m in self.metrics_history[-10:]]
        
        # Simple trend detection: if memory increased in 8 out of 10 measurements
        increases = 0
        for i in range(1, len(recent_memory)):
            if recent_memory[i] > recent_memory[i-1]:
                increases += 1
        
        potential_leak = increases >= 8
        
        if potential_leak:
            self.logger.warning("Potential memory leak detected - memory usage consistently increasing")
        
        return potential_leak


# Global performance monitor instance
performance_monitor = PerformanceMonitor()


async def start_performance_monitoring(interval_seconds: float = 30.0) -> None:
    """Start global performance monitoring."""
    await performance_monitor.start_monitoring(interval_seconds)


async def stop_performance_monitoring() -> None:
    """Stop global performance monitoring."""
    await performance_monitor.stop_monitoring()


def get_current_performance() -> Dict[str, any]:
    """Get current performance metrics."""
    return performance_monitor.get_performance_summary()


def optimize_memory() -> Dict[str, any]:
    """Optimize memory usage."""
    return performance_monitor.optimize_memory()
