<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kate LLM Client</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        
        .logo {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #667eea;
        }
        
        h1 {
            color: #333;
            margin-bottom: 1rem;
            font-size: 2.5rem;
        }
        
        .subtitle {
            color: #666;
            margin-bottom: 2rem;
            font-size: 1.1rem;
        }
        
        .features {
            text-align: left;
            margin: 2rem 0;
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 10px;
        }
        
        .feature {
            display: flex;
            align-items: center;
            margin-bottom: 0.8rem;
        }
        
        .feature:last-child {
            margin-bottom: 0;
        }
        
        .feature-icon {
            margin-right: 0.8rem;
            font-size: 1.2rem;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .status {
            margin-top: 2rem;
            padding: 1rem;
            border-radius: 10px;
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🤖</div>
        <h1>Kate</h1>
        <p class="subtitle">LLM Client with Advanced RAG</p>
        
        <div class="features">
            <div class="feature">
                <span class="feature-icon">🔍</span>
                <span>Advanced RAG with 6-service architecture</span>
            </div>
            <div class="feature">
                <span class="feature-icon">🎤</span>
                <span>Voice chat and audio processing</span>
            </div>
            <div class="feature">
                <span class="feature-icon">🌐</span>
                <span>Multiple LLM provider support</span>
            </div>
            <div class="feature">
                <span class="feature-icon">💾</span>
                <span>Persistent conversation history</span>
            </div>
        </div>
        
        <a href="/chat" class="btn">Start Chatting</a>
        <a href="/api/health" class="btn btn-secondary">System Status</a>
        
        <div class="status">
            <strong>🎉 Kate Web Interface is Live!</strong><br>
            Both desktop Qt and web interfaces are now available.
        </div>
    </div>
</body>
</html>