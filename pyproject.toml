[tool.poetry]
name = "kate"
version = "1.0.0"
description = "Kate LLM Client - A modern desktop client for multiple LLM providers"
authors = ["Kate Team <<EMAIL>>"]
license = "MIT"
readme = "README.md"
packages = [{include = "app"}]

[tool.poetry.dependencies]
python = ">=3.9,<3.13"
PySide6 = ">=6.0.0"
pydantic = ">=2.0.0"
pydantic-settings = ">=2.0.0"
sqlalchemy = ">=2.0.0"
aiosqlite = ">=0.19.0"
loguru = ">=0.7.0"
click = ">=8.0.0"
httpx = ">=0.24.0"
requests = ">=2.28.0"
aiofiles = ">=23.0.0"
pillow = ">=10.0.0"
python-dotenv = ">=1.0.0"
pyyaml = ">=6.0.0"
platformdirs = ">=4.0.0"
sentence-transformers = ">=2.0.0"
transformers = ">=4.30.0"
torch = ">=2.0.0"
numpy = ">=1.24.0"
scikit-learn = ">=1.3.0"

[tool.poetry.scripts]
kate = "app.main:main"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"