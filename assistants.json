[{"id": "kate-assistant", "name": "<PERSON>", "description": "De<PERSON><PERSON> Assistant", "model": "llama3.2:latest", "system_prompt": "You are <PERSON>, a helpful AI assistant. You provide clear, accurate, and helpful responses to user questions.", "temperature": 0.7, "max_tokens": 2048, "enabled": true}, {"id": "code-assistant", "name": "Code Assistant", "description": "Specialized coding assistant", "model": "codellama:latest", "system_prompt": "You are a coding assistant. Help users with programming questions, code review, and technical problem solving.", "temperature": 0.3, "max_tokens": 4096, "enabled": true}, {"id": "creative-assistant", "name": "Creative Assistant", "description": "Creative writing and brainstorming assistant", "model": "llama3.2:latest", "system_prompt": "You are a creative assistant. Help users with creative writing, brainstorming, and imaginative tasks.", "temperature": 0.9, "max_tokens": 3072, "enabled": true}]