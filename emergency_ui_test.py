#!/usr/bin/env python3
"""
EMERGENCY UI DIAGNOSTIC SCRIPT
Run this to identify exactly why <PERSON>'s UI isn't responding to user input.
"""

import sys
import os
import traceback
from pathlib import Path

# Add app directory to path
sys.path.insert(0, str(Path(__file__).parent / "app"))

try:
    from PySide6.QtWidgets import QApplication, QWidget
    from PySide6.QtCore import Qt, QTimer
    from PySide6.QtTest import QTest
    
    # Import Kate components
    from app.main import main
    from app.core.application import KateApplication
    from app.ui.main_window import MainWindow
    
    print("✅ All imports successful")
    
except ImportError as e:
    print(f"❌ Import failed: {e}")
    sys.exit(1)


class EmergencyUITester:
    """Emergency UI testing to identify the exact problem"""
    
    def __init__(self):
        self.app = None
        self.kate_app = None
        self.main_window = None
        self.test_results = []
    
    def run_emergency_diagnostics(self):
        """Run comprehensive emergency diagnostics"""
        print("\n🚨 EMERGENCY UI DIAGNOSTICS STARTING...")
        print("=" * 50)
        
        try:
            # Test 1: QApplication creation
            self.test_qapplication_creation()
            
            # Test 2: Kate application initialization
            self.test_kate_app_initialization()
            
            # Test 3: Main window creation
            self.test_main_window_creation()
            
            # Test 4: Widget hierarchy
            self.test_widget_hierarchy()
            
            # Test 5: Event loop
            self.test_event_loop()
            
            # Test 6: Widget interactions
            self.test_widget_interactions()
            
            # Generate report
            self.generate_emergency_report()
            
        except Exception as e:
            print(f"❌ CRITICAL ERROR in diagnostics: {e}")
            traceback.print_exc()
    
    def test_qapplication_creation(self):
        """Test QApplication creation"""
        print("\n1. Testing QApplication creation...")
        try:
            self.app = QApplication.instance()
            if not self.app:
                self.app = QApplication(sys.argv)
            
            print(f"✅ QApplication created: {self.app}")
            print(f"   - Arguments: {self.app.arguments()}")
            print(f"   - Style: {self.app.style().objectName()}")
            self.test_results.append(("QApplication", True, "Created successfully"))
            
        except Exception as e:
            print(f"❌ QApplication creation failed: {e}")
            self.test_results.append(("QApplication", False, str(e)))
    
    def test_kate_app_initialization(self):
        """Test Kate application initialization"""
        print("\n2. Testing Kate application initialization...")
        try:
            # Try to create Kate application
            self.kate_app = KateApplication()
            print(f"✅ KateApplication created: {self.kate_app}")
            
            # Check if it has required attributes
            required_attrs = ['settings', 'event_bus', 'database_manager']
            for attr in required_attrs:
                if hasattr(self.kate_app, attr):
                    value = getattr(self.kate_app, attr)
                    print(f"   - {attr}: {value}")
                else:
                    print(f"   - ❌ Missing {attr}")
            
            self.test_results.append(("KateApplication", True, "Initialized successfully"))
            
        except Exception as e:
            print(f"❌ Kate application initialization failed: {e}")
            traceback.print_exc()
            self.test_results.append(("KateApplication", False, str(e)))
    
    def test_main_window_creation(self):
        """Test main window creation"""
        print("\n3. Testing main window creation...")
        try:
            if not self.kate_app:
                print("❌ Cannot test main window - Kate app not initialized")
                return
            
            # Try to create main window
            self.main_window = MainWindow(
                self.kate_app, 
                self.kate_app.settings, 
                self.kate_app.event_bus
            )
            
            print(f"✅ MainWindow created: {self.main_window}")
            print(f"   - Size: {self.main_window.size()}")
            print(f"   - Visible: {self.main_window.isVisible()}")
            print(f"   - Enabled: {self.main_window.isEnabled()}")
            
            self.test_results.append(("MainWindow", True, "Created successfully"))
            
        except Exception as e:
            print(f"❌ Main window creation failed: {e}")
            traceback.print_exc()
            self.test_results.append(("MainWindow", False, str(e)))
    
    def test_widget_hierarchy(self):
        """Test widget hierarchy"""
        print("\n4. Testing widget hierarchy...")
        try:
            if not self.main_window:
                print("❌ Cannot test hierarchy - Main window not created")
                return
            
            # Find key components
            components = {
                'chat_area': 'ChatArea',
                'conversation_sidebar': 'ConversationSidebar', 
                'assistant_panel': 'AssistantPanel'
            }
            
            for comp_name, class_name in components.items():
                widgets = self.main_window.findChildren(QWidget, comp_name)
                if widgets:
                    widget = widgets[0]
                    print(f"✅ Found {comp_name}: {widget}")
                    print(f"   - Class: {widget.__class__.__name__}")
                    print(f"   - Visible: {widget.isVisible()}")
                    print(f"   - Enabled: {widget.isEnabled()}")
                    print(f"   - Size: {widget.size()}")
                else:
                    print(f"❌ {comp_name} not found")
            
            self.test_results.append(("WidgetHierarchy", True, "Components found"))
            
        except Exception as e:
            print(f"❌ Widget hierarchy test failed: {e}")
            self.test_results.append(("WidgetHierarchy", False, str(e)))
    
    def test_event_loop(self):
        """Test event loop functionality"""
        print("\n5. Testing event loop...")
        try:
            if not self.app:
                print("❌ Cannot test event loop - QApplication not created")
                return
            
            # Test if event loop can process events
            print("   - Testing event processing...")
            self.app.processEvents()
            
            # Test timer functionality
            timer_fired = False
            def timer_callback():
                nonlocal timer_fired
                timer_fired = True
            
            timer = QTimer()
            timer.timeout.connect(timer_callback)
            timer.setSingleShot(True)
            timer.start(100)
            
            # Process events for a short time
            for _ in range(10):
                self.app.processEvents()
                if timer_fired:
                    break
                QTest.qWait(10)
            
            if timer_fired:
                print("✅ Event loop is processing events")
                self.test_results.append(("EventLoop", True, "Processing events correctly"))
            else:
                print("❌ Event loop not processing events properly")
                self.test_results.append(("EventLoop", False, "Events not processing"))
            
        except Exception as e:
            print(f"❌ Event loop test failed: {e}")
            self.test_results.append(("EventLoop", False, str(e)))
    
    def test_widget_interactions(self):
        """Test basic widget interactions"""
        print("\n6. Testing widget interactions...")
        try:
            if not self.main_window:
                print("❌ Cannot test interactions - Main window not created")
                return
            
            # Show the main window
            self.main_window.show()
            self.app.processEvents()
            QTest.qWait(100)
            
            # Test if window is actually visible
            if self.main_window.isVisible():
                print("✅ Main window is visible")
            else:
                print("❌ Main window is not visible")
            
            # Try to find and test input widgets
            input_widgets = self.main_window.findChildren(QWidget)
            interactive_widgets = [w for w in input_widgets if w.isEnabled() and w.isVisible()]
            
            print(f"   - Found {len(interactive_widgets)} interactive widgets")
            
            # Test focus
            if interactive_widgets:
                test_widget = interactive_widgets[0]
                test_widget.setFocus()
                self.app.processEvents()
                
                if test_widget.hasFocus():
                    print(f"✅ Focus test passed on {test_widget.__class__.__name__}")
                else:
                    print(f"❌ Focus test failed on {test_widget.__class__.__name__}")
            
            self.test_results.append(("WidgetInteractions", True, f"Found {len(interactive_widgets)} interactive widgets"))
            
        except Exception as e:
            print(f"❌ Widget interaction test failed: {e}")
            traceback.print_exc()
            self.test_results.append(("WidgetInteractions", False, str(e)))
    
    def generate_emergency_report(self):
        """Generate emergency diagnostic report"""
        print("\n" + "=" * 50)
        print("🚨 EMERGENCY DIAGNOSTIC REPORT")
        print("=" * 50)
        
        passed = sum(1 for _, success, _ in self.test_results if success)
        total = len(self.test_results)
        
        print(f"Tests Passed: {passed}/{total}")
        print()
        
        for test_name, success, details in self.test_results:
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{status} {test_name}: {details}")
        
        print("\n" + "=" * 50)
        print("🎯 RECOMMENDED ACTIONS:")
        
        # Analyze results and provide recommendations
        failed_tests = [name for name, success, _ in self.test_results if not success]
        
        if "QApplication" in failed_tests:
            print("1. 🚨 CRITICAL: Fix QApplication creation in app/main.py")
        elif "KateApplication" in failed_tests:
            print("1. 🚨 CRITICAL: Fix KateApplication initialization")
        elif "MainWindow" in failed_tests:
            print("1. 🚨 CRITICAL: Fix MainWindow creation in app/ui/main_window.py")
        elif "EventLoop" in failed_tests:
            print("1. 🚨 CRITICAL: Fix event loop - check for blocking operations")
        else:
            print("1. ✅ Basic initialization working - focus on widget interactions")
        
        if "WidgetInteractions" in failed_tests:
            print("2. 🔧 Fix widget focus and interaction policies")
        
        print("3. 🧪 Test chat input/output functionality")
        print("4. 🔗 Verify signal/slot connections")
        
        print("\n💡 NEXT STEPS FOR AI:")
        print("1. Fix the highest priority issue identified above")
        print("2. Run this script again to verify the fix")
        print("3. Move to next issue until all tests pass")
        print("4. Test actual user interactions")


def main():
    """Run emergency diagnostics"""
    tester = EmergencyUITester()
    tester.run_emergency_diagnostics()


if __name__ == "__main__":
    main()
