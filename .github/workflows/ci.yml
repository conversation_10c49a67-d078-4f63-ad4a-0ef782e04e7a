# Kate LLM Desktop Client - Continuous Integration
name: CI/CD Pipeline

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master ]
  schedule:
    # Run security scans weekly
    - cron: '0 2 * * 1'

jobs:
  quality-checks:
    name: Code Quality & Security
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.9, 3.10, 3.11, 3.12]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: latest
        virtualenvs-create: true
        virtualenvs-in-project: true

    - name: Load cached venv
      id: cached-poetry-dependencies
      uses: actions/cache@v3
      with:
        path: .venv
        key: venv-${{ runner.os }}-${{ matrix.python-version }}-${{ hashFiles('**/poetry.lock') }}

    - name: Install dependencies
      if: steps.cached-poetry-dependencies.outputs.cache-hit != 'true'
      run: poetry install --no-interaction --no-root

    - name: Install project
      run: poetry install --no-interaction

    - name: Run code formatting check
      run: |
        poetry run black --check app/ tests/
        poetry run isort --check-only app/ tests/

    - name: Run linting
      run: poetry run ruff check app/ tests/

    - name: Run type checking
      run: poetry run mypy app/

    - name: Run security scan
      run: |
        poetry run bandit -r app/ -f json -o bandit-report.json
        poetry run safety check --json --output safety-report.json

    - name: Run tests with coverage
      run: |
        poetry run pytest --cov=app --cov-report=xml --cov-report=term-missing
        poetry run coverage html

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella

    - name: Upload security reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-reports-${{ matrix.python-version }}
        path: |
          bandit-report.json
          safety-report.json

  ui-tests:
    name: UI Integration Tests
    runs-on: ubuntu-latest
    needs: quality-checks

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: 3.11

    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y qt6-base-dev libgl1-mesa-dev xvfb

    - name: Install Poetry
      uses: snok/install-poetry@v1

    - name: Install dependencies
      run: poetry install --no-interaction

    - name: Run UI tests
      run: |
        export QT_QPA_PLATFORM=offscreen
        xvfb-run -a poetry run pytest tests/ui/ -v

  build-test:
    name: Build Distribution
    runs-on: ${{ matrix.os }}
    needs: [quality-checks, ui-tests]
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: 3.11

    - name: Install Poetry
      uses: snok/install-poetry@v1

    - name: Install dependencies
      run: poetry install --no-interaction

    - name: Build with PyInstaller
      run: |
        poetry run pyinstaller build/kate.spec --clean

    - name: Test built application
      run: |
        # Basic smoke test of built application
        if [ "$RUNNER_OS" == "Windows" ]; then
          ./dist/Kate.exe --version
        else
          ./dist/Kate --version
        fi
      shell: bash

    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: kate-${{ matrix.os }}
        path: dist/

  security-audit:
    name: Security Audit
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule' || github.event_name == 'push'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

  dependency-review:
    name: Dependency Review
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Dependency Review
      uses: actions/dependency-review-action@v3
      with:
        fail-on-severity: high

  release:
    name: Create Release
    runs-on: ubuntu-latest
    needs: [quality-checks, ui-tests, build-test]
    if: github.event_name == 'push' && startsWith(github.ref, 'refs/tags/v')

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download build artifacts
      uses: actions/download-artifact@v3

    - name: Create Release
      uses: softprops/action-gh-release@v1
      with:
        files: |
          kate-ubuntu-latest/*
          kate-windows-latest/*
          kate-macos-latest/*
        generate_release_notes: true
        draft: false
        prerelease: ${{ contains(github.ref, 'alpha') || contains(github.ref, 'beta') || contains(github.ref, 'rc') }}
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}