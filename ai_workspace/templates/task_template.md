
# AI TASK: {task_name}

## OBJECTIVE
{objective}

## PRE-CONDITIONS
- [ ] {precondition_1}
- [ ] {precondition_2}

## IMPLEMENTATION STEPS
1. **Diagnostic Phase**
   - Run: `python emergency_ui_test.py`
   - Analyze results
   - Identify root cause

2. **Implementation Phase**
   - Make minimal changes
   - Test after each change
   - Commit if successful

3. **Validation Phase**
   - Run: `python ai_workspace/test_suite.py`
   - Verify no regressions
   - Update progress tracking

## SUCCESS CRITERIA
- [ ] {success_criterion_1}
- [ ] {success_criterion_2}

## ROLLBACK PLAN
If this task fails:
1. `git checkout HEAD~1`
2. Run `./start_kate.sh` to verify still working
3. Try alternative approach

## TIME LIMIT
Maximum 2 hours per task
