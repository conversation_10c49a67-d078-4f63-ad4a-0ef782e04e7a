# AI DECISION JOURNAL

**Decision ID:** {decision_id}  
**Date:** {timestamp}  
**Agent:** {agent_id}  
**Category:** {decision_category}  
**Priority:** {priority_level}

---

## DECISION: {decision_title}

### **Context:** 
{situation_requiring_decision}

### **Stakeholders Affected:**
- {stakeholder_1}
- {stakeholder_2}
- {stakeholder_3}

### **Time Pressure:**
- **Decision deadline:** {deadline}
- **Implementation deadline:** {implementation_deadline}
- **Urgency level:** {urgency_level}

---

## OPTIONS CONSIDERED

### **Option A: {option_a_name}**
**Description:** {option_a_description}

**Pros:**
- {advantage_a_1}
- {advantage_a_2}
- {advantage_a_3}

**Cons:**
- {disadvantage_a_1}
- {disadvantage_a_2}
- {disadvantage_a_3}

**Risk Level:** {risk_level_a}  
**Implementation Effort:** {effort_level_a}  
**Success Probability:** {success_probability_a}%

### **Option B: {option_b_name}**
**Description:** {option_b_description}

**Pros:**
- {advantage_b_1}
- {advantage_b_2}
- {advantage_b_3}

**Cons:**
- {disadvantage_b_1}
- {disadvantage_b_2}
- {disadvantage_b_3}

**Risk Level:** {risk_level_b}  
**Implementation Effort:** {effort_level_b}  
**Success Probability:** {success_probability_b}%

### **Option C: {option_c_name}**
**Description:** {option_c_description}

**Pros:**
- {advantage_c_1}
- {advantage_c_2}
- {advantage_c_3}

**Cons:**
- {disadvantage_c_1}
- {disadvantage_c_2}
- {disadvantage_c_3}

**Risk Level:** {risk_level_c}  
**Implementation Effort:** {effort_level_c}  
**Success Probability:** {success_probability_c}%

---

## DECISION ANALYSIS

### **Evaluation Criteria:**
1. **Technical Feasibility:** {technical_weight}% weight
2. **Risk Level:** {risk_weight}% weight
3. **Implementation Speed:** {speed_weight}% weight
4. **Long-term Maintainability:** {maintainability_weight}% weight
5. **Resource Requirements:** {resource_weight}% weight

### **Scoring Matrix:**
| Criteria | Option A | Option B | Option C |
|----------|----------|----------|----------|
| Technical Feasibility | {score_a_tech}/10 | {score_b_tech}/10 | {score_c_tech}/10 |
| Risk Level | {score_a_risk}/10 | {score_b_risk}/10 | {score_c_risk}/10 |
| Implementation Speed | {score_a_speed}/10 | {score_b_speed}/10 | {score_c_speed}/10 |
| Maintainability | {score_a_maint}/10 | {score_b_maint}/10 | {score_c_maint}/10 |
| Resource Requirements | {score_a_resource}/10 | {score_b_resource}/10 | {score_c_resource}/10 |
| **Weighted Total** | **{total_score_a}/10** | **{total_score_b}/10** | **{total_score_c}/10** |

---

## DECISION MADE: {chosen_option}

### **Reasoning:**
**Primary factors:**
- {main_consideration_1}
- {main_consideration_2}
- {main_consideration_3}

**Trade-offs accepted:**
- {trade_off_1}
- {trade_off_2}
- {trade_off_3}

**Assumptions made:**
- {assumption_1}
- {assumption_2}
- {assumption_3}

### **Decision Confidence:** {confidence_level}/10

### **Dissenting Opinions:**
{dissenting_opinion_summary}

---

## EXPECTED OUTCOMES

### **Success Indicators:**
- {success_indicator_1}
- {success_indicator_2}
- {success_indicator_3}

### **Key Performance Metrics:**
- {metric_1}: Target {target_1}
- {metric_2}: Target {target_2}
- {metric_3}: Target {target_3}

### **Timeline Expectations:**
- **Immediate results:** {immediate_timeline}
- **Short-term results:** {short_term_timeline}
- **Long-term results:** {long_term_timeline}

---

## RISK MANAGEMENT

### **Potential Risks:**
**Risk 1: {risk_1_name}**
- **Probability:** {risk_1_probability}%
- **Impact:** {risk_1_impact}
- **Mitigation:** {risk_1_mitigation}

**Risk 2: {risk_2_name}**
- **Probability:** {risk_2_probability}%
- **Impact:** {risk_2_impact}
- **Mitigation:** {risk_2_mitigation}

**Risk 3: {risk_3_name}**
- **Probability:** {risk_3_probability}%
- **Impact:** {risk_3_impact}
- **Mitigation:** {risk_3_mitigation}

### **Contingency Plans:**
**If Option A fails:**
{contingency_plan_a}

**If timeline is missed:**
{timeline_contingency}

**If resources are insufficient:**
{resource_contingency}

---

## IMPLEMENTATION PLAN

### **Phase 1: Preparation**
- **Duration:** {prep_duration}
- **Activities:**
  - {prep_activity_1}
  - {prep_activity_2}
  - {prep_activity_3}

### **Phase 2: Core Implementation**
- **Duration:** {impl_duration}
- **Activities:**
  - {impl_activity_1}
  - {impl_activity_2}
  - {impl_activity_3}

### **Phase 3: Validation & Rollout**
- **Duration:** {validation_duration}
- **Activities:**
  - {validation_activity_1}
  - {validation_activity_2}
  - {validation_activity_3}

---

## FOLLOW-UP REQUIRED

### **Validation needed:**
- {validation_requirement_1}
- {validation_requirement_2}
- {validation_requirement_3}

### **Review timeline:**
- **First review:** {first_review_date}
- **Progress check:** {progress_check_date}
- **Final assessment:** {final_assessment_date}

### **Success criteria for review:**
- {review_criterion_1}
- {review_criterion_2}
- {review_criterion_3}

---

## LESSONS LEARNED (Post-Implementation)

### **What worked well:**
- {success_factor_1}
- {success_factor_2}
- {success_factor_3}

### **What could be improved:**
- {improvement_1}
- {improvement_2}
- {improvement_3}

### **Unexpected outcomes:**
- {unexpected_outcome_1}
- {unexpected_outcome_2}

### **Knowledge for future decisions:**
- {future_knowledge_1}
- {future_knowledge_2}
- {future_knowledge_3}

---

## DECISION IMPACT ASSESSMENT

### **Actual vs Expected Results:**
| Metric | Expected | Actual | Variance |
|--------|----------|--------|----------|
| {metric_1} | {expected_1} | {actual_1} | {variance_1} |
| {metric_2} | {expected_2} | {actual_2} | {variance_2} |
| {metric_3} | {expected_3} | {actual_3} | {variance_3} |

### **Overall Decision Quality:** {decision_quality_rating}/10

### **Would make same decision again:** [ ] Yes [ ] No [ ] Partially

**Explanation:** {decision_retrospective}

---

## RELATED DECISIONS
- **Previous decision:** {related_decision_1} - {relationship_1}
- **Dependent decision:** {related_decision_2} - {relationship_2}
- **Future decision:** {related_decision_3} - {relationship_3}

---

**Decision Status:** {decision_status}  
**Last Updated:** {last_updated}  
**Next Review Due:** {next_review_due}
