# AI TASK: {task_name}

**Task ID:** {task_id}  
**Created:** {timestamp}  
**Agent:** {agent_id}  
**Priority:** {priority}  
**Estimated Duration:** {estimated_duration_minutes} minutes

---

## OBJECTIVE
{objective}

## CONTEXT & BACKGROUND
### **Why this task is needed:**
{reasoning}

### **Previous attempts:**
{what_was_tried_before}

### **Key constraints:**
{limitations_and_requirements}

### **Dependencies:**
- [ ] {dependency_1}
- [ ] {dependency_2}

---

## PRE-CONDITIONS
**System State Requirements:**
- [ ] {precondition_1}
- [ ] {precondition_2}
- [ ] {precondition_3}

**Required Services:**
- [ ] {service_1} is running
- [ ] {service_2} is accessible

**File System Requirements:**
- [ ] {file_requirement_1}
- [ ] {file_requirement_2}

---

## ANALYSIS PHASE

### **Problem Investigation:**
**Root cause analysis:** {what_was_discovered}

**Alternative approaches considered:**
1. **Approach A:** {option_1}
   - **Pros:** {advantages_1}
   - **Cons:** {disadvantages_1}
   - **Risk Level:** {risk_level_1}

2. **Approach B:** {option_2}
   - **Pros:** {advantages_2}
   - **Cons:** {disadvantages_2}
   - **Risk Level:** {risk_level_2}

**Decision rationale:** {why_this_approach_was_chosen}

### **Implementation Strategy:**
**Approach selected:** {chosen_method}

**Expected challenges:** {anticipated_issues}

**Success metrics:** {how_to_measure_success}

---

## IMPLEMENTATION STEPS

### **1. Diagnostic Phase**
**Commands to run:**
```bash
python emergency_ui_test.py
python ai_workspace/test_suite.py
```

**Expected outputs:**
- {expected_diagnostic_result_1}
- {expected_diagnostic_result_2}

**Analysis checklist:**
- [ ] Identify root cause
- [ ] Confirm problem scope
- [ ] Validate assumptions

### **2. Implementation Phase**
**Step 2.1: {implementation_step_1}**
- **Action:** {specific_action_1}
- **Files to modify:** {files_1}
- **Expected result:** {expected_result_1}
- **Test command:** {test_command_1}

**Step 2.2: {implementation_step_2}**
- **Action:** {specific_action_2}
- **Files to modify:** {files_2}
- **Expected result:** {expected_result_2}
- **Test command:** {test_command_2}

**Step 2.3: {implementation_step_3}**
- **Action:** {specific_action_3}
- **Files to modify:** {files_3}
- **Expected result:** {expected_result_3}
- **Test command:** {test_command_3}

### **3. Validation Phase**
**Tests to run:**
```bash
python ai_workspace/test_suite.py
python emergency_ui_test.py
./start_kate.sh  # Verify no regressions
```

**Validation checklist:**
- [ ] All tests pass
- [ ] No regressions introduced
- [ ] Performance not degraded
- [ ] Error handling works correctly

---

## IMPLEMENTATION LOG

### **Changes Made:**
**File:** {file_path_1}
- **What changed:** {specific_modifications_1}
- **Why changed:** {reasoning_for_change_1}
- **Lines modified:** {line_numbers_1}
- **Impact:** {expected_effect_1}

**File:** {file_path_2}
- **What changed:** {specific_modifications_2}
- **Why changed:** {reasoning_for_change_2}
- **Lines modified:** {line_numbers_2}
- **Impact:** {expected_effect_2}

### **Testing Results:**
**Test:** {test_name_1}
- **Command:** {test_command_1}
- **Result:** {pass_fail_1}
- **Output:** {test_output_1}
- **Issues found:** {problems_discovered_1}

**Test:** {test_name_2}
- **Command:** {test_command_2}
- **Result:** {pass_fail_2}
- **Output:** {test_output_2}
- **Issues found:** {problems_discovered_2}

---

## SUCCESS CRITERIA
**Primary objectives:**
- [ ] {success_criterion_1}
- [ ] {success_criterion_2}
- [ ] {success_criterion_3}

**Performance requirements:**
- [ ] {performance_requirement_1}
- [ ] {performance_requirement_2}

**Quality gates:**
- [ ] Code review passed
- [ ] All tests passing
- [ ] Documentation updated
- [ ] No security vulnerabilities introduced

---

## ROLLBACK PLAN
**If this task fails:**

**Step 1: Immediate Recovery**
```bash
git checkout HEAD~1
./start_kate.sh  # Verify still working
```

**Step 2: Alternative Approach**
{alternative_approach_description}

**Step 3: Escalation**
- Document failure reason
- Update task with lessons learned
- Request guidance from supervisor AI

**Recovery validation:**
- [ ] System returns to previous working state
- [ ] No data loss occurred
- [ ] All services still functional

---

## COMPLETION SUMMARY

### **What Was Accomplished:**
**Primary objective:** {main_goal_achieved}

**Secondary benefits:** {additional_improvements}

**Technical debt created:** {future_work_needed}

### **Key Learnings:**
**Insights gained:** {what_was_learned}

**Patterns discovered:** {reusable_knowledge}

**Mistakes to avoid:** {what_not_to_do_next_time}

### **Recommendations for Future Work:**
**Next logical steps:** {suggested_follow_up_tasks}

**Related improvements:** {connected_enhancements}

**Technical debt to address:** {cleanup_needed}

---

## METRICS & ANALYTICS

### **Time Tracking:**
- **Planned duration:** {estimated_duration_minutes} minutes
- **Actual duration:** {actual_duration_minutes} minutes
- **Efficiency ratio:** {efficiency_percentage}%

### **Quality Metrics:**
- **Files modified:** {files_modified_count}
- **Lines added:** {lines_added}
- **Lines removed:** {lines_removed}
- **Tests added:** {tests_added}
- **Tests passing:** {tests_passing}/{total_tests}

### **Impact Assessment:**
- **Risk level:** {final_risk_assessment}
- **Stability impact:** {stability_impact}
- **Performance impact:** {performance_impact}
- **User experience impact:** {ux_impact}

---

## VALIDATION CHECKLIST

### **Code Quality:**
- [ ] Code follows project style guidelines
- [ ] No code duplication introduced
- [ ] Error handling is comprehensive
- [ ] Logging is appropriate
- [ ] Comments explain complex logic

### **Testing:**
- [ ] Unit tests cover new functionality
- [ ] Integration tests pass
- [ ] Edge cases are handled
- [ ] Error conditions are tested
- [ ] Performance tests pass

### **Documentation:**
- [ ] Code is self-documenting
- [ ] API changes are documented
- [ ] README updated if needed
- [ ] Architecture docs updated if needed

### **Security:**
- [ ] No sensitive data exposed
- [ ] Input validation implemented
- [ ] Authentication/authorization respected
- [ ] No new security vulnerabilities

---

## SIGN-OFF

**Task completed by:** {agent_id}  
**Completion timestamp:** {completion_timestamp}  
**Reviewed by:** {reviewer_agent_id}  
**Review timestamp:** {review_timestamp}  

**Final status:** {final_status}  
**Overall success rating:** {success_rating}/10

**Supervisor approval:** [ ] Approved [ ] Needs revision

---

**Time Limit:** Maximum {max_duration_hours} hours  
**Next Review:** {next_review_timestamp}
