#!/usr/bin/env python3
"""
AI Task Manager for Kate LLM Client Development.

Comprehensive task management system that integrates enhanced documentation,
decision tracking, knowledge base, and multi-AI coordination.
"""

import json
import uuid
from dataclasses import asdict, dataclass
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from loguru import logger


@dataclass
class TaskProgress:
    """Enhanced task progress tracking"""
    task_id: str
    task_name: str
    status: str  # "not_started", "in_progress", "completed", "failed", "cancelled"
    agent_id: str
    priority: str  # "low", "medium", "high", "critical"
    
    # Time tracking
    created_at: str
    started_at: Optional[str] = None
    completed_at: Optional[str] = None
    estimated_duration_minutes: int = 60
    actual_duration_minutes: Optional[int] = None
    
    # Context and reasoning
    objective: str = ""
    reasoning: str = ""
    constraints: List[str] = None
    dependencies: List[str] = None
    
    # Implementation tracking
    files_modified: List[str] = None
    tests_run: List[str] = None
    issues_found: List[str] = None
    
    # Completion details
    accomplishments: List[str] = None
    learnings: List[str] = None
    recommendations: List[str] = None
    
    # Quality metrics
    success_rating: Optional[int] = None  # 1-10
    code_quality_score: Optional[int] = None  # 1-10
    
    def __post_init__(self):
        if self.constraints is None:
            self.constraints = []
        if self.dependencies is None:
            self.dependencies = []
        if self.files_modified is None:
            self.files_modified = []
        if self.tests_run is None:
            self.tests_run = []
        if self.issues_found is None:
            self.issues_found = []
        if self.accomplishments is None:
            self.accomplishments = []
        if self.learnings is None:
            self.learnings = []
        if self.recommendations is None:
            self.recommendations = []


@dataclass
class DecisionRecord:
    """Decision tracking record"""
    decision_id: str
    decision_title: str
    agent_id: str
    timestamp: str
    context: str
    options_considered: List[Dict[str, Any]]
    chosen_option: str
    reasoning: str
    confidence_level: int  # 1-10
    impact_assessment: str
    follow_up_required: List[str]


@dataclass
class KnowledgePattern:
    """Knowledge base pattern"""
    pattern_id: str
    pattern_name: str
    category: str
    confidence: int  # 1-10
    problem_description: str
    solution_approach: str
    implementation_details: str
    success_indicators: List[str]
    common_pitfalls: List[str]
    related_patterns: List[str]
    evidence: List[str]  # Task IDs where this was used
    success_rate: float  # 0.0 to 1.0


class AITaskManager:
    """
    Comprehensive AI task management system.
    
    Integrates enhanced task tracking, decision logging, knowledge base,
    and multi-AI coordination for autonomous development.
    """
    
    def __init__(self, workspace_dir: str = "ai_workspace"):
        self.workspace_dir = Path(workspace_dir)
        self.workspace_dir.mkdir(exist_ok=True)
        
        # Data storage
        self.tasks: Dict[str, TaskProgress] = {}
        self.decisions: Dict[str, DecisionRecord] = {}
        self.knowledge_patterns: Dict[str, KnowledgePattern] = {}
        
        # File paths
        self.tasks_file = self.workspace_dir / "tasks.json"
        self.decisions_file = self.workspace_dir / "decisions.json"
        self.knowledge_file = self.workspace_dir / "knowledge_base.json"
        
        self.logger = logger.bind(component="AITaskManager")
        
        # Load existing data
        self._load_data()
    
    def create_task(self, task_name: str, objective: str, agent_id: str,
                   priority: str = "medium", estimated_duration: int = 60,
                   reasoning: str = "", constraints: List[str] = None,
                   dependencies: List[str] = None) -> str:
        """Create a new task with enhanced tracking"""
        
        task_id = str(uuid.uuid4())
        
        task = TaskProgress(
            task_id=task_id,
            task_name=task_name,
            status="not_started",
            agent_id=agent_id,
            priority=priority,
            created_at=datetime.now().isoformat(),
            estimated_duration_minutes=estimated_duration,
            objective=objective,
            reasoning=reasoning,
            constraints=constraints or [],
            dependencies=dependencies or []
        )
        
        self.tasks[task_id] = task
        self._save_tasks()
        
        # Generate task documentation
        self._generate_task_documentation(task)
        
        self.logger.info(f"Created task {task_id}: {task_name}")
        return task_id
    
    def start_task(self, task_id: str) -> bool:
        """Start a task and update tracking"""
        if task_id not in self.tasks:
            self.logger.error(f"Task {task_id} not found")
            return False
        
        task = self.tasks[task_id]
        task.status = "in_progress"
        task.started_at = datetime.now().isoformat()
        
        self._save_tasks()
        self.logger.info(f"Started task {task_id}: {task.task_name}")
        return True
    
    def complete_task(self, task_id: str, accomplishments: List[str] = None,
                     learnings: List[str] = None, recommendations: List[str] = None,
                     success_rating: int = None, files_modified: List[str] = None,
                     tests_run: List[str] = None) -> bool:
        """Complete a task with detailed outcome tracking"""
        
        if task_id not in self.tasks:
            self.logger.error(f"Task {task_id} not found")
            return False
        
        task = self.tasks[task_id]
        task.status = "completed"
        task.completed_at = datetime.now().isoformat()
        
        # Calculate actual duration
        if task.started_at:
            start_time = datetime.fromisoformat(task.started_at)
            end_time = datetime.fromisoformat(task.completed_at)
            task.actual_duration_minutes = int((end_time - start_time).total_seconds() / 60)
        
        # Update completion details
        if accomplishments:
            task.accomplishments = accomplishments
        if learnings:
            task.learnings = learnings
        if recommendations:
            task.recommendations = recommendations
        if success_rating:
            task.success_rating = success_rating
        if files_modified:
            task.files_modified = files_modified
        if tests_run:
            task.tests_run = tests_run
        
        self._save_tasks()
        
        # Update task documentation
        self._update_task_documentation(task)
        
        # Extract knowledge patterns
        self._extract_knowledge_patterns(task)
        
        self.logger.info(f"Completed task {task_id}: {task.task_name}")
        return True
    
    def record_decision(self, decision_title: str, agent_id: str, context: str,
                       options_considered: List[Dict[str, Any]], chosen_option: str,
                       reasoning: str, confidence_level: int,
                       impact_assessment: str = "", follow_up: List[str] = None) -> str:
        """Record a decision with full context"""
        
        decision_id = str(uuid.uuid4())
        
        decision = DecisionRecord(
            decision_id=decision_id,
            decision_title=decision_title,
            agent_id=agent_id,
            timestamp=datetime.now().isoformat(),
            context=context,
            options_considered=options_considered,
            chosen_option=chosen_option,
            reasoning=reasoning,
            confidence_level=confidence_level,
            impact_assessment=impact_assessment,
            follow_up_required=follow_up or []
        )
        
        self.decisions[decision_id] = decision
        self._save_decisions()
        
        # Generate decision documentation
        self._generate_decision_documentation(decision)
        
        self.logger.info(f"Recorded decision {decision_id}: {decision_title}")
        return decision_id
    
    def add_knowledge_pattern(self, pattern_name: str, category: str,
                            problem_description: str, solution_approach: str,
                            implementation_details: str, success_indicators: List[str],
                            common_pitfalls: List[str] = None,
                            confidence: int = 5) -> str:
        """Add a knowledge pattern to the base"""
        
        pattern_id = str(uuid.uuid4())
        
        pattern = KnowledgePattern(
            pattern_id=pattern_id,
            pattern_name=pattern_name,
            category=category,
            confidence=confidence,
            problem_description=problem_description,
            solution_approach=solution_approach,
            implementation_details=implementation_details,
            success_indicators=success_indicators,
            common_pitfalls=common_pitfalls or [],
            related_patterns=[],
            evidence=[],
            success_rate=0.0
        )
        
        self.knowledge_patterns[pattern_id] = pattern
        self._save_knowledge()
        
        # Generate knowledge documentation
        self._generate_knowledge_documentation(pattern)
        
        self.logger.info(f"Added knowledge pattern {pattern_id}: {pattern_name}")
        return pattern_id
    
    def generate_session_report(self, session_id: str = None) -> str:
        """Generate comprehensive session report"""
        
        if session_id is None:
            session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Get recent tasks (last 24 hours)
        cutoff_time = datetime.now().timestamp() - 86400
        recent_tasks = [
            task for task in self.tasks.values()
            if datetime.fromisoformat(task.created_at).timestamp() > cutoff_time
        ]
        
        # Get recent decisions
        recent_decisions = [
            decision for decision in self.decisions.values()
            if datetime.fromisoformat(decision.timestamp).timestamp() > cutoff_time
        ]
        
        # Generate report
        report_lines = [
            f"# AI Development Session Report - {session_id}",
            f"Generated: {datetime.now().isoformat()}",
            "",
            "## Session Summary",
            f"- Tasks Created: {len([t for t in recent_tasks if t.status == 'not_started'])}",
            f"- Tasks Completed: {len([t for t in recent_tasks if t.status == 'completed'])}",
            f"- Tasks In Progress: {len([t for t in recent_tasks if t.status == 'in_progress'])}",
            f"- Decisions Made: {len(recent_decisions)}",
            "",
            "## Task Outcomes"
        ]
        
        for task in recent_tasks:
            if task.status == "completed":
                report_lines.extend([
                    f"### ✅ {task.task_name}",
                    f"- **Agent:** {task.agent_id}",
                    f"- **Duration:** {task.actual_duration_minutes or 'N/A'} minutes",
                    f"- **Success Rating:** {task.success_rating or 'N/A'}/10",
                    f"- **Files Modified:** {len(task.files_modified)}",
                    ""
                ])
                
                if task.accomplishments:
                    report_lines.append("**Accomplishments:**")
                    for accomplishment in task.accomplishments:
                        report_lines.append(f"- {accomplishment}")
                    report_lines.append("")
                
                if task.learnings:
                    report_lines.append("**Key Learnings:**")
                    for learning in task.learnings:
                        report_lines.append(f"- {learning}")
                    report_lines.append("")
        
        if recent_decisions:
            report_lines.extend([
                "## Key Decisions Made"
            ])
            
            for decision in recent_decisions:
                report_lines.extend([
                    f"### 🎯 {decision.decision_title}",
                    f"- **Agent:** {decision.agent_id}",
                    f"- **Confidence:** {decision.confidence_level}/10",
                    f"- **Chosen Option:** {decision.chosen_option}",
                    f"- **Reasoning:** {decision.reasoning}",
                    ""
                ])
        
        return "\n".join(report_lines)
    
    def _generate_task_documentation(self, task: TaskProgress) -> None:
        """Generate enhanced task documentation"""
        template_path = self.workspace_dir / "templates" / "enhanced_task_template.md"
        
        if template_path.exists():
            with open(template_path, 'r') as f:
                template = f.read()
            
            # Fill in template variables with safe formatting
            template_vars = {
                'task_name': task.task_name,
                'task_id': task.task_id,
                'timestamp': task.created_at,
                'agent_id': task.agent_id,
                'priority': task.priority,
                'estimated_duration_minutes': task.estimated_duration_minutes,
                'objective': task.objective,
                'reasoning': task.reasoning,
                'what_was_tried_before': '[TO BE FILLED]',
                'limitations_and_requirements': ', '.join(task.constraints),
                'dependency_1': task.dependencies[0] if task.dependencies else '[TO BE FILLED]',
                'dependency_2': task.dependencies[1] if len(task.dependencies) > 1 else '[TO BE FILLED]',
            }

            # Add placeholder values for all other template variables
            template_vars.update({f"placeholder_{i}": "[TO BE FILLED]" for i in range(100)})

            # Use safe string formatting
            try:
                filled_template = template.format(**template_vars)
            except KeyError as e:
                # If template has variables we don't have, create a simplified version
                filled_template = f"""# AI TASK: {task.task_name}

**Task ID:** {task.task_id}
**Created:** {task.created_at}
**Agent:** {task.agent_id}
**Priority:** {task.priority}

## OBJECTIVE
{task.objective}

## REASONING
{task.reasoning}

## CONSTRAINTS
{', '.join(task.constraints)}

## DEPENDENCIES
{', '.join(task.dependencies)}

[Template will be completed during task execution]
"""
            
            # Save task documentation
            task_doc_path = self.workspace_dir / "tasks" / f"{task.task_id}.md"
            task_doc_path.parent.mkdir(exist_ok=True)
            
            with open(task_doc_path, 'w') as f:
                f.write(filled_template)
    
    def _update_task_documentation(self, task: TaskProgress) -> None:
        """Update task documentation with completion details"""
        # Implementation would update the task documentation with actual results
        pass
    
    def _generate_decision_documentation(self, decision: DecisionRecord) -> None:
        """Generate decision documentation"""
        # Implementation would generate decision journal entry
        pass
    
    def _generate_knowledge_documentation(self, pattern: KnowledgePattern) -> None:
        """Generate knowledge base documentation"""
        # Implementation would generate knowledge base entry
        pass
    
    def _extract_knowledge_patterns(self, task: TaskProgress) -> None:
        """Extract reusable patterns from completed tasks"""
        # Implementation would analyze task for reusable patterns
        pass
    
    def _load_data(self) -> None:
        """Load existing data from files"""
        try:
            if self.tasks_file.exists():
                with open(self.tasks_file, 'r') as f:
                    tasks_data = json.load(f)
                    self.tasks = {tid: TaskProgress(**data) for tid, data in tasks_data.items()}
            
            if self.decisions_file.exists():
                with open(self.decisions_file, 'r') as f:
                    decisions_data = json.load(f)
                    self.decisions = {did: DecisionRecord(**data) for did, data in decisions_data.items()}
            
            if self.knowledge_file.exists():
                with open(self.knowledge_file, 'r') as f:
                    knowledge_data = json.load(f)
                    self.knowledge_patterns = {pid: KnowledgePattern(**data) for pid, data in knowledge_data.items()}
                    
        except Exception as e:
            self.logger.error(f"Error loading data: {e}")
    
    def _save_tasks(self) -> None:
        """Save tasks to file"""
        try:
            with open(self.tasks_file, 'w') as f:
                json.dump({tid: asdict(task) for tid, task in self.tasks.items()}, f, indent=2)
        except Exception as e:
            self.logger.error(f"Error saving tasks: {e}")
    
    def _save_decisions(self) -> None:
        """Save decisions to file"""
        try:
            with open(self.decisions_file, 'w') as f:
                json.dump({did: asdict(decision) for did, decision in self.decisions.items()}, f, indent=2)
        except Exception as e:
            self.logger.error(f"Error saving decisions: {e}")
    
    def _save_knowledge(self) -> None:
        """Save knowledge patterns to file"""
        try:
            with open(self.knowledge_file, 'w') as f:
                json.dump({pid: asdict(pattern) for pid, pattern in self.knowledge_patterns.items()}, f, indent=2)
        except Exception as e:
            self.logger.error(f"Error saving knowledge: {e}")


# Global task manager instance
ai_task_manager = AITaskManager()


def create_ai_task(task_name: str, objective: str, agent_id: str, **kwargs) -> str:
    """Convenience function for creating tasks"""
    return ai_task_manager.create_task(task_name, objective, agent_id, **kwargs)


def complete_ai_task(task_id: str, **kwargs) -> bool:
    """Convenience function for completing tasks"""
    return ai_task_manager.complete_task(task_id, **kwargs)


def record_ai_decision(decision_title: str, agent_id: str, context: str, **kwargs) -> str:
    """Convenience function for recording decisions"""
    return ai_task_manager.record_decision(decision_title, agent_id, context, **kwargs)
