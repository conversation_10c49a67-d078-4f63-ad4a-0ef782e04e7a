#!/usr/bin/env python3
'''
Automated test suite for AI development
'''

import subprocess
import sys
import json
from pathlib import Path

def run_test_suite():
    '''Run complete test suite'''
    
    results = {
        'emergency_diagnostics': False,
        'kate_startup': False,
        'ui_responsiveness': False,
        'chat_functionality': False
    }
    
    print("🧪 Running AI Test Suite...")
    
    # Test 1: Emergency diagnostics
    try:
        result = subprocess.run([sys.executable, 'emergency_ui_test.py'], 
                              capture_output=True, text=True, timeout=60)
        results['emergency_diagnostics'] = result.returncode == 0
        print(f"✅ Emergency diagnostics: {results['emergency_diagnostics']}")
    except Exception as e:
        print(f"❌ Emergency diagnostics failed: {e}")
    
    # Test 2: Kate startup
    try:
        result = subprocess.run(['./start_kate.sh'], 
                              capture_output=True, text=True, timeout=30)
        results['kate_startup'] = "GUI should be visible" in result.stdout
        print(f"✅ Kate startup: {results['kate_startup']}")
    except Exception as e:
        print(f"❌ Kate startup failed: {e}")
    
    # Save results
    with open(Path('ai_workspace/test_results.json'), 'w') as f:
        json.dump(results, f, indent=2)
    
    # Overall success
    success_rate = sum(results.values()) / len(results)
    print(f"\n📊 Overall Success Rate: {success_rate:.1%}")
    
    return success_rate > 0.5

if __name__ == "__main__":
    success = run_test_suite()
    sys.exit(0 if success else 1)
