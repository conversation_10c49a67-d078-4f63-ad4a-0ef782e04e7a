"""
Test suite for keyboard shortcuts and accessibility features.
"""

import pytest
from PySide6.QtCore import Qt
from PySide6.QtGui import QKeySequence
from PySide6.QtTest import QTest
from PySide6.QtWidgets import QApplication

from app.ui.main_window import MainWindow
from app.core.events import EventBus


class TestKeyboardShortcuts:
    """Test keyboard shortcuts functionality."""
    
    @pytest.fixture
    def main_window(self, qtbot):
        """Create a main window for testing."""
        event_bus = EventBus()
        window = MainWindow(event_bus, None)
        qtbot.addWidget(window)
        return window
    
    def test_new_conversation_shortcut(self, main_window, qtbot):
        """Test Ctrl+N creates new conversation."""
        # Simulate Ctrl+N key press
        qtbot.keySequence(main_window, QKeySequence("Ctrl+N"))
        
        # Verify new conversation was created (would need to check conversation sidebar)
        assert True  # Placeholder - would verify conversation creation
    
    def test_focus_input_shortcut(self, main_window, qtbot):
        """Test Ctrl+L focuses chat input."""
        # Simulate Ctrl+L key press
        qtbot.keySequence(main_window, QKeySequence("Ctrl+L"))
        
        # Verify chat input has focus
        if hasattr(main_window.chat_area, 'input_area'):
            # Would check if input area has focus
            assert True  # Placeholder
    
    def test_toggle_sidebar_shortcut(self, main_window, qtbot):
        """Test Ctrl+B toggles sidebar visibility."""
        initial_visibility = main_window.conversation_sidebar.isVisible()
        
        # Simulate Ctrl+B key press
        qtbot.keySequence(main_window, QKeySequence("Ctrl+B"))
        
        # Verify sidebar visibility changed
        assert main_window.conversation_sidebar.isVisible() != initial_visibility
    
    def test_toggle_assistant_panel_shortcut(self, main_window, qtbot):
        """Test Ctrl+Shift+A toggles assistant panel."""
        initial_visibility = main_window.assistant_panel.isVisible()
        
        # Simulate Ctrl+Shift+A key press
        qtbot.keySequence(main_window, QKeySequence("Ctrl+Shift+A"))
        
        # Verify assistant panel visibility changed
        assert main_window.assistant_panel.isVisible() != initial_visibility
    
    def test_fullscreen_shortcut(self, main_window, qtbot):
        """Test F11 toggles fullscreen mode."""
        initial_fullscreen = main_window.isFullScreen()
        
        # Simulate F11 key press
        qtbot.keySequence(main_window, QKeySequence("F11"))
        
        # Verify fullscreen state changed
        assert main_window.isFullScreen() != initial_fullscreen
    
    def test_font_size_shortcuts(self, main_window, qtbot):
        """Test font size accessibility shortcuts."""
        initial_font_size = main_window.font().pointSize()
        
        # Test increase font size (Ctrl++)
        qtbot.keySequence(main_window, QKeySequence("Ctrl++"))
        assert main_window.font().pointSize() > initial_font_size
        
        # Test decrease font size (Ctrl+-)
        qtbot.keySequence(main_window, QKeySequence("Ctrl+-"))
        qtbot.keySequence(main_window, QKeySequence("Ctrl+-"))
        assert main_window.font().pointSize() < main_window.font().pointSize()
        
        # Test reset font size (Ctrl+0)
        qtbot.keySequence(main_window, QKeySequence("Ctrl+0"))
        assert main_window.font().pointSize() == 10  # Default size
    
    def test_quit_shortcut(self, main_window, qtbot):
        """Test Ctrl+Q closes application."""
        # This test would need special handling since it closes the app
        # For now, just verify the shortcut exists
        assert hasattr(main_window, 'shortcut_quit')
        assert main_window.shortcut_quit.key() == QKeySequence("Ctrl+Q")
    
    def test_minimize_shortcut(self, main_window, qtbot):
        """Test Ctrl+M minimizes window."""
        # Show window first
        main_window.show()
        qtbot.waitForWindowShown(main_window)
        
        # Simulate Ctrl+M key press
        qtbot.keySequence(main_window, QKeySequence("Ctrl+M"))
        
        # Verify window is minimized
        assert main_window.isMinimized()


class TestAccessibilityFeatures:
    """Test accessibility features."""
    
    @pytest.fixture
    def main_window(self, qtbot):
        """Create a main window for testing."""
        event_bus = EventBus()
        window = MainWindow(event_bus, None)
        qtbot.addWidget(window)
        return window
    
    def test_font_size_limits(self, main_window, qtbot):
        """Test font size has proper limits."""
        # Test maximum font size limit
        for _ in range(20):  # Try to exceed max
            main_window._increase_font_size()
        
        assert main_window.font().pointSize() <= 24  # Max limit
        
        # Test minimum font size limit
        for _ in range(20):  # Try to go below min
            main_window._decrease_font_size()
        
        assert main_window.font().pointSize() >= 8  # Min limit
    
    def test_keyboard_navigation(self, main_window, qtbot):
        """Test keyboard navigation between UI elements."""
        # Test Tab navigation
        main_window.show()
        qtbot.waitForWindowShown(main_window)
        
        # Simulate Tab key presses
        qtbot.keyClick(main_window, Qt.Key_Tab)
        qtbot.keyClick(main_window, Qt.Key_Tab)
        
        # Verify focus moves between elements
        assert True  # Would verify focus chain
    
    def test_high_contrast_support(self, main_window, qtbot):
        """Test high contrast mode support."""
        # This would test theme switching for accessibility
        # Placeholder for now
        assert True
    
    def test_screen_reader_compatibility(self, main_window, qtbot):
        """Test screen reader compatibility."""
        # Verify accessibility properties are set
        assert main_window.accessibleName() or main_window.windowTitle()
        
        # Check that important UI elements have accessible names
        if hasattr(main_window, 'chat_area'):
            # Would verify chat area has proper accessibility attributes
            assert True
    
    def test_keyboard_only_operation(self, main_window, qtbot):
        """Test that all functionality is accessible via keyboard."""
        # This would test that all major functions can be performed
        # without mouse interaction
        main_window.show()
        qtbot.waitForWindowShown(main_window)
        
        # Test creating new conversation via keyboard
        qtbot.keySequence(main_window, QKeySequence("Ctrl+N"))
        
        # Test focusing input via keyboard
        qtbot.keySequence(main_window, QKeySequence("Ctrl+L"))
        
        # Test toggling panels via keyboard
        qtbot.keySequence(main_window, QKeySequence("Ctrl+B"))
        qtbot.keySequence(main_window, QKeySequence("Ctrl+Shift+A"))
        
        assert True  # All operations completed without mouse


class TestUIResponsiveness:
    """Test UI responsiveness and performance."""
    
    @pytest.fixture
    def main_window(self, qtbot):
        """Create a main window for testing."""
        event_bus = EventBus()
        window = MainWindow(event_bus, None)
        qtbot.addWidget(window)
        return window
    
    def test_responsive_resize(self, main_window, qtbot):
        """Test responsive UI behavior during window resize."""
        main_window.show()
        qtbot.waitForWindowShown(main_window)
        
        # Test different window sizes
        test_sizes = [(1000, 700), (1400, 900), (1920, 1080)]
        
        for width, height in test_sizes:
            main_window.resize(width, height)
            qtbot.wait(200)  # Wait for resize to complete
            
            # Verify splitter proportions are appropriate
            sizes = main_window.main_splitter.sizes()
            total_width = sum(sizes)
            
            # Check that proportions are reasonable
            assert all(size > 0 for size in sizes)  # All panels visible
            assert total_width > 0
    
    def test_keyboard_shortcut_response_time(self, main_window, qtbot):
        """Test keyboard shortcuts respond quickly."""
        import time
        
        main_window.show()
        qtbot.waitForWindowShown(main_window)
        
        # Measure response time for shortcuts
        start_time = time.time()
        qtbot.keySequence(main_window, QKeySequence("Ctrl+B"))
        response_time = time.time() - start_time
        
        # Should respond within 100ms
        assert response_time < 0.1
    
    def test_memory_usage_stability(self, main_window, qtbot):
        """Test that UI operations don't cause memory leaks."""
        import gc
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss
        
        # Perform many UI operations
        for _ in range(100):
            qtbot.keySequence(main_window, QKeySequence("Ctrl+B"))
            qtbot.keySequence(main_window, QKeySequence("Ctrl+B"))
            qtbot.wait(10)
        
        # Force garbage collection
        gc.collect()
        
        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory
        
        # Memory increase should be reasonable (less than 10MB)
        assert memory_increase < 10 * 1024 * 1024
