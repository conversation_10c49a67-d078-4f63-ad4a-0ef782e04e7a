#!/usr/bin/env python3
"""
AI Worker Setup Script
Sets up everything an AI needs to work autonomously on <PERSON>
"""

import os
import sys
import json
import subprocess
from pathlib import Path


def setup_ai_workspace():
    """Set up the AI workspace with all necessary tools"""
    
    print("🤖 Setting up AI workspace for autonomous Kate development...")
    
    # 1. Create AI workspace directory
    ai_dir = Path("ai_workspace")
    ai_dir.mkdir(exist_ok=True)
    
    # 2. Create progress tracking
    progress_file = ai_dir / "progress.json"
    if not progress_file.exists():
        initial_progress = {
            "current_phase": "emergency_ui_fix",
            "completed_tasks": [],
            "failed_attempts": [],
            "last_working_commit": None,
            "critical_issues": [],
            "success_metrics": {
                "ui_responsive": False,
                "chat_functional": False,
                "no_crashes": False
            }
        }
        
        with open(progress_file, 'w') as f:
            json.dump(initial_progress, f, indent=2)
    
    # 3. Create AI instruction templates
    create_ai_templates(ai_dir)
    
    # 4. Set up automated testing
    setup_automated_testing(ai_dir)
    
    # 5. Create recovery scripts
    create_recovery_scripts(ai_dir)
    
    print("✅ AI workspace setup complete!")
    print(f"📁 Workspace location: {ai_dir.absolute()}")
    print("\n🚀 Ready for AI autonomous development!")


def create_ai_templates(ai_dir):
    """Create AI instruction templates"""
    
    templates_dir = ai_dir / "templates"
    templates_dir.mkdir(exist_ok=True)
    
    # Task template
    task_template = """
# AI TASK: {task_name}

## OBJECTIVE
{objective}

## PRE-CONDITIONS
- [ ] {precondition_1}
- [ ] {precondition_2}

## IMPLEMENTATION STEPS
1. **Diagnostic Phase**
   - Run: `python emergency_ui_test.py`
   - Analyze results
   - Identify root cause

2. **Implementation Phase**
   - Make minimal changes
   - Test after each change
   - Commit if successful

3. **Validation Phase**
   - Run: `python ai_workspace/test_suite.py`
   - Verify no regressions
   - Update progress tracking

## SUCCESS CRITERIA
- [ ] {success_criterion_1}
- [ ] {success_criterion_2}

## ROLLBACK PLAN
If this task fails:
1. `git checkout HEAD~1`
2. Run `./start_kate.sh` to verify still working
3. Try alternative approach

## TIME LIMIT
Maximum 2 hours per task
"""
    
    with open(templates_dir / "task_template.md", 'w') as f:
        f.write(task_template)


def setup_automated_testing(ai_dir):
    """Set up automated testing suite"""
    
    test_suite = f"""#!/usr/bin/env python3
'''
Automated test suite for AI development
'''

import subprocess
import sys
import json
from pathlib import Path

def run_test_suite():
    '''Run complete test suite'''
    
    results = {{
        'emergency_diagnostics': False,
        'kate_startup': False,
        'ui_responsiveness': False,
        'chat_functionality': False
    }}
    
    print("🧪 Running AI Test Suite...")
    
    # Test 1: Emergency diagnostics
    try:
        result = subprocess.run([sys.executable, 'emergency_ui_test.py'], 
                              capture_output=True, text=True, timeout=60)
        results['emergency_diagnostics'] = result.returncode == 0
        print(f"✅ Emergency diagnostics: {{results['emergency_diagnostics']}}")
    except Exception as e:
        print(f"❌ Emergency diagnostics failed: {{e}}")
    
    # Test 2: Kate startup
    try:
        result = subprocess.run(['./start_kate.sh'], 
                              capture_output=True, text=True, timeout=30)
        results['kate_startup'] = "GUI should be visible" in result.stdout
        print(f"✅ Kate startup: {{results['kate_startup']}}")
    except Exception as e:
        print(f"❌ Kate startup failed: {{e}}")
    
    # Save results
    with open(Path('ai_workspace/test_results.json'), 'w') as f:
        json.dump(results, f, indent=2)
    
    # Overall success
    success_rate = sum(results.values()) / len(results)
    print(f"\\n📊 Overall Success Rate: {{success_rate:.1%}}")
    
    return success_rate > 0.5

if __name__ == "__main__":
    success = run_test_suite()
    sys.exit(0 if success else 1)
"""
    
    test_file = ai_dir / "test_suite.py"
    with open(test_file, 'w') as f:
        f.write(test_suite)
    
    # Make executable
    os.chmod(test_file, 0o755)


def create_recovery_scripts(ai_dir):
    """Create recovery scripts for when things go wrong"""
    
    # Panic recovery script
    panic_script = """#!/bin/bash
# PANIC RECOVERY SCRIPT
# Use when AI breaks everything

echo "🚨 PANIC RECOVERY ACTIVATED"

# Go back to last known working state
git checkout main
git clean -fd

# Test if Kate still starts
echo "Testing Kate startup..."
timeout 30s ./start_kate.sh

if [ $? -eq 0 ]; then
    echo "✅ Kate is working again"
    echo "📝 AI can resume from clean state"
else
    echo "❌ Kate is completely broken"
    echo "🆘 HUMAN INTERVENTION REQUIRED"
fi
"""
    
    panic_file = ai_dir / "panic_recovery.sh"
    with open(panic_file, 'w') as f:
        f.write(panic_script)
    
    os.chmod(panic_file, 0o755)
    
    # Progress tracker
    progress_script = """#!/usr/bin/env python3
'''
Progress tracking for AI development
'''

import json
import sys
from datetime import datetime
from pathlib import Path

def update_progress(task_name, status, details=""):
    '''Update AI progress tracking'''
    
    progress_file = Path('ai_workspace/progress.json')
    
    if progress_file.exists():
        with open(progress_file, 'r') as f:
            progress = json.load(f)
    else:
        progress = {
            "completed_tasks": [],
            "failed_attempts": [],
            "success_metrics": {}
        }
    
    # Update progress
    entry = {
        "task": task_name,
        "status": status,
        "timestamp": datetime.now().isoformat(),
        "details": details
    }
    
    if status == "completed":
        progress["completed_tasks"].append(entry)
    elif status == "failed":
        progress["failed_attempts"].append(entry)
    
    # Save updated progress
    with open(progress_file, 'w') as f:
        json.dump(progress, f, indent=2)
    
    print(f"📝 Progress updated: {task_name} - {status}")

if __name__ == "__main__":
    if len(sys.argv) >= 3:
        task_name = sys.argv[1]
        status = sys.argv[2]
        details = sys.argv[3] if len(sys.argv) > 3 else ""
        update_progress(task_name, status, details)
    else:
        print("Usage: python progress_tracker.py <task_name> <status> [details]")
"""
    
    progress_file = ai_dir / "progress_tracker.py"
    with open(progress_file, 'w') as f:
        f.write(progress_script)


def main():
    """Main setup function"""
    setup_ai_workspace()
    
    # Run initial diagnostics
    print("\n🔍 Running initial diagnostics...")
    try:
        subprocess.run([sys.executable, "emergency_ui_test.py"], timeout=60)
    except Exception as e:
        print(f"⚠️  Initial diagnostics failed: {e}")
    
    print("\n" + "="*50)
    print("🤖 AI WORKER READY FOR AUTONOMOUS DEVELOPMENT")
    print("="*50)
    print("\nNext steps for AI:")
    print("1. Run: python emergency_ui_test.py")
    print("2. Fix the highest priority issue identified")
    print("3. Run: python ai_workspace/test_suite.py")
    print("4. Repeat until Kate is fully functional")
    print("\nEmergency recovery: bash ai_workspace/panic_recovery.sh")


if __name__ == "__main__":
    main()
